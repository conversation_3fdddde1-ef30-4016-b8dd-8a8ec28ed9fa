package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.fileEditor.*
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.intellij.openapi.vfs.VirtualFile
import com.think1024.tocodesign.ideaplugin.webview.LoadingMode
import com.think1024.tocodesign.ideaplugin.webview.TocoBrowserConfig
import com.think1024.tocodesign.ideaplugin.webview.WebViewLoader
import java.beans.PropertyChangeListener
import javax.swing.JComponent

class TocoWebViewEditor(private val project: Project, private val file: VirtualFile, private val url: String) : FileEditor {
    private val logger = com.intellij.openapi.diagnostic.Logger.getInstance(TocoWebViewEditor::class.java)
    private val webViewLoader: WebViewLoader
    private val userDataMap = mutableMapOf<Key<*>, Any?>()
    init {
        logger.info("TocoWebViewEditor 初始化: $url")

        // 创建 WebViewLoader
        webViewLoader = WebViewLoader(
            project = project,
            url = url,
            TocoBrowserConfig(isFrame = false, isOSR = true, file = file),
            onInitPage = {
                logger.info("页面加载完成回调")
            },
            loadingMode = LoadingMode.INIT_PAGE_CALLBACK, // 明确指定加载模式
            onModifiedChanged = {
                ApplicationManager.getApplication().invokeLater(Runnable {
                    FileEditorManager.getInstance(project).updateFilePresentation(file)
                    FileEditorManager.getInstance(project).updateFileColor(file)
                })
            }
        )
    }

    // 获取编辑器的组件
    override fun getComponent(): JComponent {
        return webViewLoader.getComponent()
    }

    // 获取首选的焦点组件
    override fun getPreferredFocusedComponent(): JComponent? {
        return webViewLoader.getPreferredFocusedComponent()
    }

    override fun getName(): String {
        // 调用公共方法获取最新标题，而非直接访问private字段
        return (file as? TocoVirtualFile)?.getDisplayName() ?: "WebView"
    }

    // 重写 getFile 方法，修复弃用警告
    override fun getFile(): VirtualFile {
        return file
    }

    // 设置编辑器的状态
    override fun setState(state: FileEditorState) {
        if (state is TocoWebViewState) {
            // 使用异步处理以避免UI阻塞
            ApplicationManager.getApplication().executeOnPooledThread {
                // 如果URL发生变化，才重新加载
                if (webViewLoader.getBrowser().browser.cefBrowser.url != state.url && state.url.isNotEmpty()) {
                    logger.info("setState: 重新加载URL: ${state.url}")
                    webViewLoader.loadURL(state.url)
                }
            }
        }
    }

    override fun isModified(): Boolean {
        return webViewLoader.getBrowser().getModified()
    }

    // 获取编辑器的状态
    override fun getState(level: FileEditorStateLevel): FileEditorState {
        val file = this.file as? TocoVirtualFile
        return TocoWebViewState(
            url = file?.webUrl ?: "",
            title = file?.name ?: "",
            type = file?.getUserData(TocoVirtualFile.TYPE_KEY) ?: "java"
        )
    }

    // 添加属性变化监听器
    override fun addPropertyChangeListener(listener: PropertyChangeListener) {
        // 可以为空实现，除非你需要监听属性的变化
    }

    // 移除属性变化监听器
    override fun removePropertyChangeListener(listener: PropertyChangeListener) {
        // 可以为空实现，除非你需要监听属性的变化
    }

    // 检查编辑器是否有效
    override fun isValid(): Boolean {
        return true
    }

    // 处理编辑器的释放
    override fun dispose() {
        logger.info("dispose: 释放资源")
        webViewLoader.dispose()
    }

    // 实现 UserDataHolder 接口
    override fun <T : Any?> getUserData(key: Key<T>): T? {
        @Suppress("UNCHECKED_CAST")
        return userDataMap[key] as T?
    }

    override fun <T : Any?> putUserData(key: Key<T>, value: T?) {
        if (value == null) {
            userDataMap.remove(key)
        } else {
            userDataMap[key] = value
        }
    }

    fun openDevTools() {
        logger.info("openDevTools: 打开开发者工具")
        webViewLoader.openDevTools()
    }

    /**
     * 刷新当前页面
     */
    fun refresh() {
        logger.info("refresh: 执行手动刷新")

        // 检查是否有待更新的URL
        val tocoFile = file as? TocoVirtualFile
        val targetUrl = tocoFile?.pendingUrl ?: tocoFile?.webUrl ?: url

        if (tocoFile?.pendingUrl != null) {
            // 如果有新的待更新URL，先同步状态
            tocoFile.webUrl = tocoFile.pendingUrl!!  // 更新当前显示的URL
            tocoFile.putUserData(TocoVirtualFile.URL_KEY, tocoFile.pendingUrl)
            // 加载目标URL
            logger.info("refresh: 加载URL: $targetUrl")
            webViewLoader.loadURL(targetUrl)
            // 清除待更新URL
            tocoFile.pendingUrl = null
        } else {
            // 如果没有新URL，只刷新当前页面
            webViewLoader.refresh()
        }
    }
}
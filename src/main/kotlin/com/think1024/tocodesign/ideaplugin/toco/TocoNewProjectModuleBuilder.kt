// Copyright 2000-2019 JetBrains s.r.o. Use of this source code is governed by the Apache 2.0 license that can be found in the LICENSE file.
package com.think1024.tocodesign.ideaplugin.toco

import com.fasterxml.jackson.databind.ObjectMapper
import com.intellij.ide.plugins.PluginManager
import com.intellij.ide.util.projectWizard.EmptyWebProjectTemplate
import com.intellij.ide.util.projectWizard.SettingsStep
import com.intellij.ide.util.projectWizard.WebTemplateNewProjectWizardBase
import com.intellij.ide.util.projectWizard.WebTemplateProjectWizardStep
import com.intellij.ide.wizard.GeneratorNewProjectWizardBuilderAdapter
import com.intellij.ide.wizard.NewProjectWizardBaseStep
import com.intellij.ide.wizard.NewProjectWizardStep
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.module.ModifiableModuleModel
import com.intellij.openapi.module.Module
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.ProgressManager
import com.intellij.openapi.progress.Task
import com.intellij.openapi.project.Project
import com.intellij.openapi.projectRoots.JavaSdk
import com.intellij.openapi.projectRoots.Sdk
import com.intellij.openapi.roots.ui.configuration.JdkComboBox
import com.intellij.openapi.roots.ui.configuration.SdkListItem
import com.intellij.openapi.roots.ui.configuration.projectRoot.ProjectSdksModel
import com.intellij.openapi.ui.ValidationInfo
import com.intellij.openapi.util.Condition
import com.intellij.openapi.util.IconLoader
import com.intellij.openapi.util.text.StringUtil
import com.intellij.platform.ProjectGeneratorPeer
import com.intellij.ui.JBColor
import com.think1024.tocodesign.ideaplugin.services.TocoService
import com.think1024.tocodesign.ideaplugin.services.UserService
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.ui.ComboItem
import com.think1024.tocodesign.ideaplugin.ui.LoginHandler
import com.think1024.tocodesign.ideaplugin.ui.createComboBox
import com.think1024.tocodesign.ideaplugin.utils.JacksonUtil.toList
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import org.json.JSONArray
import java.awt.Dimension
import java.nio.file.Paths
import java.util.concurrent.CompletableFuture
import javax.swing.*


class TocoProjectTemplate() : EmptyWebProjectTemplate() {
    private val logger = Logger.getInstance(TocoProjectTemplate::class.java)
    var step: NewProjectWizardBaseStep? = null
    var generateConfig: BoilerplateCmd.GenerateProjectConfig? = null

    data class TocoProjectSetting(
//        var gitRepo: String? = null,
        var description: String? = null,
        var dbType: String? = null,
        var groupId: String? = null,
        var organizationId: Long? = null,
        var projectName: String? = null
    )

    private  fun createProject(config: Any): Pair<String?, String?> {
        try {
            val jsonString = ObjectMapper().writeValueAsString(config)
            val (response, message) = TocoService.post("url.path.create.project", jsonString)
            return if (response != null && response is String) {
                Pair(response, null)
            } else {
                Pair(null, message)
            }
        } catch (e: Exception) {
            logger.warn("Failed to create project", e)
            return Pair(null, e.message)
        }
    }

    override fun getName(): String {
        return "Toco Project"
    }

    override fun getIcon(): Icon {
        return IconLoader.getIcon("/icons/toco_color.svg", TocoProjectTemplate::class.java)
    }

    override fun createPeer(): ProjectGeneratorPeer<Any> {
        return object : ProjectGeneratorPeer<Any> {
            val settings = ApplicationPluginSettings.getInstance()
//            val gitInput = JTextField()
//            val width = 200
            val groupNameInput = JTextField().apply {
                preferredSize = Dimension(width, preferredSize.height)
            }
            val descInput = JTextField().apply {
                preferredSize = Dimension(width, preferredSize.height)
            }
            val errorLabel = JLabel("").apply {
                foreground = JBColor.RED
            }
            private val sdksModel = ProjectSdksModel().apply {
                syncSdks()
            }
            fun isJDKGreaterThanOrEqual11(versionString: String?): Boolean {
                if (versionString == null) {
                    return false
                }
                try {
                    // 提取 JDK 主版本号
                    val version = versionString.replace("[^0-9.]".toRegex(), "")
                    val majorVersion =
                        version.split("\\.".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()[0].toInt()
                    return majorVersion >= 11
                } catch (e: NumberFormatException) {
                    return false
                }
            }
            private val jdkFilter: Condition<Sdk?> = Condition { sdk ->
                if (sdk == null || sdk.sdkType !is JavaSdk) {
                    return@Condition false
                }
                return@Condition isJDKGreaterThanOrEqual11(sdk.versionString)
            }
            private val jdkSuggestFilter: Condition<SdkListItem.SuggestedItem?> = Condition { suggest ->
                if (suggest == null || suggest.sdkType !is JavaSdk) {
                    return@Condition false
                }
                return@Condition isJDKGreaterThanOrEqual11(suggest.version)
            }
            private val jdkComboBox = JdkComboBox(null, sdksModel, null, jdkFilter, jdkSuggestFilter, null, null).apply {
                preferredSize = Dimension(width, preferredSize.height)
                selectedJdk = sdksModel.sdks.firstOrNull()
            }
            val dbTypes: List<ComboItem<String>> = listOf(
                ComboItem("MySQL", "mysql"),
                ComboItem("PostgreSQL", "postgresql"),
                ComboItem("Oracle", "oracle"),
                ComboItem("Dameng", "dm")
            )
            val databaseComboBox = createComboBox(dbTypes).apply {
                selectedItem = dbTypes.firstOrNull()
                preferredSize = Dimension(width, preferredSize.height)
            }
            val orgComboBox = createComboBox<Long?>(listOf(
                ComboItem(getI18nString("new.project.wizard.org.loading"), null)
            )).apply {
                preferredSize = Dimension(width, preferredSize.height)
            }

            val usernameLabel = JLabel(getI18nString("account.not.logged.in")).apply {
                setBounds(0, 0, width, preferredSize.height)
            }
            val loginButton = JButton(getI18nString("account.login")).apply {
                addActionListener {
                    LoginHandler.getInstance().handleLogin {
                        updateSettingsUI()
                        loadOrgs()
                    }
                }
            }
            val userPanel = JPanel().apply {
                add(usernameLabel)
                add(loginButton)
            }

            init {
                loadOrgs()
                step?.name = ""
                step?.nameProperty?.afterChange {
                    if (it.isNotEmpty()) {
                        groupNameInput.text = "com.${it.replace("-", "_")}"
                    } else {
                        groupNameInput.text =  ""
                    }
                }
            }

            private fun loadOrgs() {
                if (settings.loggedIn) {
                    CompletableFuture.supplyAsync {
                        UserService.getInstance().updateOrgs()
                    }.thenAccept { orgs ->
                        orgComboBox.removeAllItems()
                        if (orgs != null) {
                            orgs.forEach { if (it != null) orgComboBox.addItem(ComboItem(it.name, it.id)) }
                            val first = orgs.firstOrNull()
                            if (first != null) {
                                val item = ComboItem<Long?>(first.name, first.id)
                                orgComboBox.setSelectedItem(item)
                            }
                        } else {
                            orgComboBox.addItem(
                                ComboItem(getI18nString("new.project.wizard.org.load.failed"), null)
                            )
                        }
                    }
                } else {
                    orgComboBox.removeAllItems()
                    orgComboBox.addItem(
                        ComboItem(getI18nString("account.not.logged.in"), null)
                    )
                }
            }


            private fun updateSettingsUI() {
                if (settings.loggedIn) {
                    usernameLabel.text =
                        "${getI18nString("account.title")}: ${settings.username} (${settings.nickname})"
                    loginButton.isVisible = false
                } else {
                    usernameLabel.text = getI18nString("account.not.logged.in")
                    loginButton.isVisible = true
                }
            }

            override fun getComponent(): JComponent {
                return JPanel()
            }

            override fun buildUI(settingsStep: SettingsStep) {
//                settingsStep.addSettingsField("${getI18nString("new.project.wizard.git.repo")}:", gitInput)
                settingsStep.addSettingsField("${getI18nString("new.project.wizard.description")}:", descInput)
                settingsStep.addSettingsField("JDK:", jdkComboBox)
                settingsStep.addSettingsField("${getI18nString("new.project.wizard.database")}:", databaseComboBox)
                settingsStep.addSettingsField("${getI18nString("new.project.wizard.group.name")}:", groupNameInput)
                settingsStep.addSettingsField("${getI18nString("new.project.wizard.org")}:", orgComboBox)
                settingsStep.addSettingsField("${getI18nString("account.title")}:", userPanel)
                settingsStep.addSettingsComponent(errorLabel)
                updateSettingsUI()
            }

            override fun getSettings(): Any {
                return generateConfig!!
            }

            private fun doValidate(): ValidationInfo? {
                val name = step?.name
                if (name.isNullOrEmpty()) {
                    return ValidationInfo(getI18nString("new.project.wizard.project.name.empty"))
                }
                var path = step?.path
                if (path.isNullOrEmpty()) {
                    return ValidationInfo(getI18nString("new.project.wizard.location.empty"))
                }
                path = Paths.get(path, name).toString()
                if (groupNameInput.text.isBlank()) {
                    return ValidationInfo(getI18nString("new.project.wizard.group.name.empty"), groupNameInput)
                }
                val jdk = jdkComboBox.selectedItem
                if (jdk?.jdk == null) {
                    return ValidationInfo(getI18nString("new.project.wizard.jdk.empty"), jdkComboBox)
                }
                if (!isJDKGreaterThanOrEqual11(jdk.jdk?.versionString)) {
                    return ValidationInfo(getI18nString("new.project.wizard.jdk.goe.11"), jdkComboBox)
                }
                if (databaseComboBox.selectedItem == null) {
                    return ValidationInfo(getI18nString("new.project.wizard.database.empty"), databaseComboBox)
                }
                val appSettings = ApplicationPluginSettings.getInstance()
                if (!appSettings.loggedIn) {
                    return ValidationInfo(getI18nString("account.not.logged.in"), loginButton)
                }
                val settings = TocoProjectSetting().apply {
//                    gitRepo = gitInput.text
                    description = descInput.text
                    groupId = groupNameInput.text
                    dbType = (databaseComboBox.selectedItem as ComboItem<String?>).value
                    organizationId = (orgComboBox.selectedItem as ComboItem<Long?>).value
                    projectName = name
                }

                val pluginDescriptor = PluginManager.getPluginByClass(TocoProjectTemplate::class.java)
                val pluginVersion =  pluginDescriptor?.version ?: "1.0.0"
                // 获取vsversion
                val timeout = 5
                val defaultMessage = getI18nString("new.project.wizard.create.project.failed")
                val (version, versionMessage) = TocoService.get(
                    "url.path.create.project.version",
                    mapOf("clientVersion" to pluginVersion),
                    emptyMap(),
                    timeout
                )
                if (version == null || version !is String) {
                    return ValidationInfo(versionMessage ?: defaultMessage)
                }
                // 获取项目渲染配置
                val (configs, configMessage) = TocoService.get(
                    "url.path.create.project.config",
                    mapOf("type" to "restore"),
                    emptyMap(),
                    timeout
                )
                if (configs == null || configs !is JSONArray ||  configs.length() == 0) {
                    return ValidationInfo(configMessage ?: defaultMessage)
                }
                // 找到第一个可用的配置
                val config = configs.toList().find{ config ->
                    config is LinkedHashMap<*, *> &&
                            (config as LinkedHashMap<String, *>).getValue("enabled") == true &&
                            config.getValue("jsonschema") != null
                } as LinkedHashMap<String, String>?
                if (config == null) {
                    return ValidationInfo(defaultMessage)
                }
                // 在toco服务端创建项目
                val (projectId, msg) = createProject(settings)
                if (projectId == null) {
                    return ValidationInfo(msg ?: defaultMessage)
                }
                val url = config.getValue("url")
                val ref = config.getValue("ref")
//                val gitUrl = settings.gitRepo
                val groupId = settings.groupId
                val result: FossilResult = Fossil.createProject(Item(projectId, name))
                if (!result.success) {
                    return ValidationInfo(result.message ?: defaultMessage)
                }
                // 生成config
                generateConfig = BoilerplateCmd.GenerateProjectConfig(url, ref, version, name, projectId, path, groupId)
                // 设置jdk
                step?.context?.projectJdk = jdk.jdk
                return null
            }

            override fun validate(): ValidationInfo? {
                val info = doValidate()
                if (info != null) {
                    errorLabel.text = info.message
                } else {
                    errorLabel.text = ""
                }
                return info
            }

            override fun isBackgroundJobRunning(): Boolean {
                return false
            }
        }
    }
}

class TocoNewProjectWizard(private val template: TocoProjectTemplate) : WebTemplateNewProjectWizardBase() {
    override val id: String = template.javaClass.name
    override val name: String = StringUtil.capitalizeWords(template.name, true)
    override val icon: Icon = template.icon

    fun getTemplate(): TocoProjectTemplate {
        return template
    }

    override fun createTemplateStep(parent: NewProjectWizardBaseStep): NewProjectWizardStep {
        template.step = parent
        return WebTemplateProjectWizardStep(parent, template)
    }
}

class TocoNewProjectModuleBuilder : GeneratorNewProjectWizardBuilderAdapter(
    TocoNewProjectWizard(
        TocoProjectTemplate()
    )
) {
    private val logger = Logger.getInstance(TocoNewProjectModuleBuilder::class.java)
    override fun commitModule(project: Project, model: ModifiableModuleModel?): Module? {
        // 不调用super的commitModule，如果调用会当成module处理，
        // 直接获取渲染project的设置自行渲染
        val template = (this.wizard as TocoNewProjectWizard).getTemplate()
        val config = template.generateConfig
        if (config != null) {
            ProgressManager.getInstance().run(object : Task.Modal(project, "", false) {
                override fun run(indicator: ProgressIndicator) {
                    indicator.text = "${getI18nString("new.project.wizard.generate.file")}..."
                    indicator.isIndeterminate = true
                    val result = Fossil.initProject(Item(config.projectId, config.projectName), config.path) { _, _ ->
                        val res = BoilerplateCmd.generateProject(config)
                        FossilResult(res.exitCode == 0, message = res.error)
                    }
                    if (!result.success) {
                        logger.warn("[Toco generateProject] ${result.message}")
                        indicator.text2 = result.message
                    }

                    indicator.isIndeterminate = false
                    indicator.fraction = 1.0
                }
            })
        }
        return null
    }
}


package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.ui.JBColor
import com.intellij.ui.LayeredIcon
import com.intellij.util.IconUtil
import com.intellij.util.ui.UIUtil
import java.awt.*
import java.awt.image.BufferedImage
import javax.swing.Icon

object BadgeIconUtil {
    fun createBadgedIcon(baseIcon: Icon): Icon {
        // 创建一个简单的红点图标
        val badgeSize = 6
        val badgeImage = UIUtil.createImage(null, badgeSize, badgeSize, BufferedImage.TYPE_INT_ARGB)
        val g2d = badgeImage.createGraphics()

        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)
        
        // 绘制红色圆形背景
        g2d.color = JBColor.RED
        g2d.fillOval(0, 0, badgeSize, badgeSize)

        // 白色边框
        g2d.color = JBColor.WHITE
        g2d.drawOval(0, 0, badgeSize - 1, badgeSize - 1)

        g2d.dispose()

        // 使用LayeredIcon来组合基础图标和badge
        val badgeIcon = IconUtil.createImageIcon(badgeImage)
        val layeredIcon = LayeredIcon(2)
        layeredIcon.setIcon(baseIcon, 0)
        layeredIcon.setIcon(badgeIcon, 1, baseIcon.iconWidth - badgeSize, 0)

        return layeredIcon
    }
}
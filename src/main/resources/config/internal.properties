# 内部测试环境配置 (internal)
# Internal testing environment configuration

# project info (internal - same as runIde)
project.plugin.id=com.think1024.speedsters.ideaplugin
project.name=speedsters-auto

# plugin display info
plugin.display.name=Speedsters Auto
plugin.vendor.url=https://toco.teitui.com/
plugin.vendor.email=<EMAIL>

# desktop protocol
locator.desktop.url.protocol=tocoapp
locator.desktop.url.protocol.test=tocotest

# hosts (internal - same as runIde)
url.host.default=https://toco-dev1.teitui.com
url.host.test=https://toco-dev1.teitui.com
url.frontend.host.default=https://toco-dev1.teitui.com
url.frontend.host.test=https://local.teitui.com

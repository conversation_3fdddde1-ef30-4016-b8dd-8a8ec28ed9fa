package com.think1024.tocodesign.ideaplugin.actions

import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.fileEditor.FileEditor
import com.intellij.openapi.fileEditor.FileEditorManager
import com.think1024.tocodesign.ideaplugin.toco.TocoWebViewEditor

class HookSplitEditorAction(origAction: AnAction): HookAction(origAction) {
    fun isTocoFile(e: AnActionEvent): Boolean {
        val project = e.project ?: return false
        val file = e.getData(CommonDataKeys.VIRTUAL_FILE) ?: return false
        val editors: Array<FileEditor?> = FileEditorManager.getInstance(project).getEditors(file)
        for (editor in editors) {
            if (editor is TocoWebViewEditor) {
                return true
            }
        }
        return false
    }

    override fun actionPerformed(e: AnActionEvent) {
        if (isTocoFile(e)) {
            return
        }
        origAction.actionPerformed(e)
    }

    override fun update(e: AnActionEvent) {
        super.update(e)
        e.presentation.isEnabledAndVisible = !isTocoFile(e)
    }
}

fun hookSplitActions() {
    val actionManager = ActionManager.getInstance()
    listOf("SplitHorizontally", "SplitVertically", "OpenEditorInOppositeTabGroup").forEach {
        actionManager.replaceAction(it, HookSplitEditorAction(actionManager.getAction(it)))
    }
}

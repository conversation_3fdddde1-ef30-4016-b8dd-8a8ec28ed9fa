package com.think1024.tocodesign.ideaplugin.services

import com.intellij.ide.highlighter.JavaFileType
import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import com.intellij.openapi.roots.ProjectRootManager
import com.intellij.openapi.vfs.VfsUtilCore
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.*
import com.intellij.psi.search.FileTypeIndex
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.psi.search.PsiShortNamesCache
import com.intellij.psi.util.PsiTreeUtil
import com.think1024.tocodesign.ideaplugin.utils.MatchUtil.fuzzyMatchWithIndices
import com.think1024.tocodesign.ideaplugin.utils.MatchUtil.highlightMatches
import kotlinx.coroutines.suspendCancellableCoroutine
import org.jetbrains.kotlin.idea.KotlinFileType
import org.jetbrains.kotlin.psi.*
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

@Service(Service.Level.PROJECT)
class StructureAnalyzerService(private val project: Project) {

    private val logger = Logger.getInstance(StructureAnalyzerService::class.java)

    data class FileStructure(
        val name: String,
        val highlightedName: String, // 高亮后的文件名
        val path: String,
        val absPath: String,
        val score: Int = 0 // 匹配分数
    )
    data class ClassStructure(
        val className: String,
        val highlightedName: String, // 高亮后的文件名
        val methods: List<String>,
        val score: Int = 0 // 添加分数属性，默认为0
    )
    data class MethodDetail(
        val methodName: String,
        val parameters: List<ParameterDetail>,
        val returnType: String,
        val bodyText: String,
        val wrappedClassText: String
    )
    data class ParameterDetail(val name: String, val type: String)

    suspend fun getFileList(project: Project, keyword: String): List<FileStructure> = suspendCancellableCoroutine { cont ->
        DumbService.getInstance(project).runWhenSmart {
            try {
                cont.resume(analyzeStructure(keyword))
            } catch (e: Exception) {
                cont.resumeWithException(e)
            }
        }
    }

    suspend fun getMethod(project: Project, className: String, methodName: String): List<MethodDetail> =
        suspendCancellableCoroutine { cont ->
            DumbService.getInstance(project).runWhenSmart {
                try {
                    cont.resume(analyzeMethodDetailsByClassAndName(className, methodName))
                } catch (e: Exception) {
                    cont.resumeWithException(e)
                }
            }
        }

    fun analyzeStructure(keyword: String): List<FileStructure> {
        val psiManager = PsiManager.getInstance(project)
        val result = mutableListOf<FileStructure>()

        ReadAction.run<RuntimeException> {
            // 通过 ProjectRootManager 获取项目内容根目录
            val contentRoots = ProjectRootManager.getInstance(project).contentRoots

            // 遍历所有内容根目录下的文件
            contentRoots.forEach { root ->
                // 递归遍历目录下的所有文件
                VfsUtilCore.iterateChildrenRecursively(root, null) { virtualFile ->
                    // 只处理文件，跳过目录
                    if (!virtualFile.isDirectory && virtualFile.isValid && !virtualFile.name.endsWith(".class")) {
                        val psiFile: PsiFile? = psiManager.findFile(virtualFile)
                        if (psiFile != null) {
                            // 模糊匹配文件名
                            val matchResult = fuzzyMatchWithIndices(keyword, psiFile.name)
                            val score = matchResult.score

                            if (score > 0) { // 只添加匹配的文件
                                val highlightedName = highlightMatches(psiFile.name, matchResult.matchedIndices)

                                result.add(
                                    FileStructure(
                                        name = psiFile.name,
                                        highlightedName = highlightedName,
                                        // 计算相对于项目根目录的路径
                                        path = VfsUtilCore.getRelativePath(virtualFile, project.baseDir) ?: virtualFile.path,
                                        absPath = virtualFile.path,
                                        score = score
                                    )
                                )
                            }
                        }
                    }
                    // 继续遍历子目录
                    true
                }
            }
        }

        // 按分数排序并限制结果
        val sortedResult = result.sortedByDescending { it.score }
        logger.info("Found ${sortedResult.size} files matching keyword: $keyword")
        return sortedResult.take(30)
    }


    private fun isPrimitiveType(type: String): Boolean {
        return type in setOf(
            "int", "long", "double", "float", "boolean", "char", "byte", "short",
            "Integer", "Long", "Double", "Float", "Boolean", "Char", "Byte", "Short",
            "String", "Unit", "Any", "void"
        )
    }

    private fun extractNestedTypes(type: String): List<String> {
        val cleaned = type.replace(" ", "")
        val result = mutableListOf<String>()
        val sb = StringBuilder()
        var angleDepth = 0
        for (c in cleaned) {
            when (c) {
                '<' -> {
                    angleDepth++
                    if (angleDepth == 1) {
                        result.add(sb.toString())
                        sb.clear()
                        continue
                    }
                }
                '>' -> {
                    angleDepth--
                    if (angleDepth == 0) {
                        result.addAll(extractNestedTypes(sb.toString()))
                        sb.clear()
                        continue
                    }
                }
                ',' -> {
                    if (angleDepth == 1) {
                        result.add(sb.toString())
                        sb.clear()
                        continue
                    }
                }
            }
            sb.append(c)
        }
        if (sb.isNotEmpty()) result.add(sb.toString())
        return result.map { it.trim().substringBefore("[") }
    }

    // ✅ 位于 class StructureAnalyzerService 内部（和 resolveTypeStructure 同级）
    private fun extractAllNestedTypes(type: String): List<String> {
        val cleaned = type.replace(Regex("[\\[\\]()<>]"), "")
        val tokens = cleaned.split("<", ">", ",", " ").map { it.trim() }
        return tokens.filter { it.isNotBlank() }
    }

    private fun resolveTypeStructure(
        typeName: String,
        project: Project,
        scope: GlobalSearchScope,
        visited: MutableSet<String>
    ): String {
        val rawName = typeName.substringBefore("<").substringBefore("[")
        if (isPrimitiveType(rawName) || visited.contains(rawName)) return ""

        visited.add(rawName)

        val psiFacade = JavaPsiFacade.getInstance(project)
        val classes = psiFacade.findClasses(rawName, scope)
            .ifEmpty {
                PsiShortNamesCache.getInstance(project).getClassesByName(rawName, scope)
            }

        if (classes.isEmpty()) return ""

        val psiClass = classes.first()
        val bodyLines = psiClass.text.lines()
            .dropWhile { it.trim().startsWith("package") || it.trim().startsWith("import") || it.trim().isBlank() }
        val bodyText = bodyLines.joinToString("\n")

        val nestedTypes = psiClass.allFields
            .flatMap { extractAllNestedTypes(it.type.presentableText) }
            .filter { !isPrimitiveType(it) && it != rawName }
            .distinct()
            .joinToString("\n\n") { resolveTypeStructure(it, project, scope, visited) }

        return if (nestedTypes.isNotBlank()) "$bodyText\n\n$nestedTypes" else bodyText
    }

    fun analyzeMethodDetailsByClassAndName(className: String, methodName: String): List<MethodDetail> {
        val psiManager = PsiManager.getInstance(project)
        val scope = GlobalSearchScope.projectScope(project)
        val result = mutableListOf<MethodDetail>()

        ReadAction.run<RuntimeException> {
            val kotlinFiles = FileTypeIndex.getFiles(KotlinFileType.INSTANCE, scope)
            for (vf in kotlinFiles) {
                val psiFile = psiManager.findFile(vf) as? KtFile ?: continue
                val classes = PsiTreeUtil.findChildrenOfType(psiFile, KtClass::class.java)
                for (ktClass in classes) {
                    if (ktClass.name != className) continue
                    val methods = ktClass.declarations.filterIsInstance<KtNamedFunction>().filter { it.name == methodName }
                    for (func in methods) {
                        val parameters = func.valueParameters.map {
                            ParameterDetail(it.name ?: "<unnamed>", it.typeReference?.text ?: "Unknown")
                        }
                        val returnType = func.typeReference?.text ?: "Unit"
                        val methodText = func.text
                        val annotations = ktClass.annotationEntries.joinToString("\n") { it.text }
                        val classHeader = "${annotations}\nclass ${ktClass.name} {".trim()
                        val indentedMethod = methodText.prependIndent("    ")
                        val classText = """
$classHeader
    // ...
$indentedMethod
    // ...
}
                        """.trimIndent()

                        val visited = mutableSetOf<String>()
                        val allTypes = parameters.map { it.type } + returnType
                        val extraDefs = allTypes
                            .flatMap { extractNestedTypes(it) }
                            .filter { !isPrimitiveType(it) }
                            .distinct()
                            .joinToString("\n\n") { resolveTypeStructure(it, project, scope, visited) }

                        val wrapped = if (extraDefs.isNotBlank()) "$classText\n\n// ==== 类型结构 ====\n$extraDefs" else classText
                        result.add(MethodDetail(func.name!!, parameters, returnType, methodText, wrapped))
                    }
                }
            }

            val javaFiles = FileTypeIndex.getFiles(JavaFileType.INSTANCE, scope)
            for (vf in javaFiles) {
                val psiFile = psiManager.findFile(vf) as? PsiJavaFile ?: continue
                for (cls in psiFile.classes) {
                    if (cls.name != className) continue
                    val methods = cls.methods.filter { it.name == methodName }
                    for (method in methods) {
                        val parameters = method.parameterList.parameters.map {
                            ParameterDetail(it.name ?: "<unnamed>", it.type.presentableText)
                        }
                        val returnType = method.returnType?.presentableText ?: "void"
                        val methodText = method.text
                        val annotations = cls.modifierList?.annotations?.joinToString("\n") { it.text } ?: ""
                        val classHeader = "${annotations}\n${cls.modifierList?.text.orEmpty()} class ${cls.name} {".trim()
                        val indentedMethod = methodText.prependIndent("    ")
                        val classText = """
$classHeader
    // ...
$indentedMethod
    // ...
}
                        """.trimIndent()

                        val visited = mutableSetOf<String>()
                        val allTypes = parameters.map { it.type } + returnType
                        val extraDefs = allTypes
                            .flatMap { extractNestedTypes(it) }
                            .filter { !isPrimitiveType(it) }
                            .distinct()
                            .joinToString("\n\n") { resolveTypeStructure(it, project, scope, visited) }

                        val wrapped = if (extraDefs.isNotBlank()) "$classText\n\n// ==== 类型结构 ====\n$extraDefs" else classText
                        result.add(MethodDetail(method.name, parameters, returnType, methodText, wrapped))
                    }
                }
            }
        }

        logger.info("Found ${result.size} method(s) in class '$className' with name '$methodName'.")
        return result
    }
}

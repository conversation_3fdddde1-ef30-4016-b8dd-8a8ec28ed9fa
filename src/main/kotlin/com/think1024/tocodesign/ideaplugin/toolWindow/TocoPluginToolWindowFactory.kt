package com.think1024.tocodesign.ideaplugin.toolWindow

import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowFactory
import com.intellij.openapi.wm.ToolWindowManager
import com.intellij.ui.content.ContentFactory
import com.intellij.ui.content.ContentManagerEvent
import com.intellij.ui.content.ContentManagerListener
import com.intellij.openapi.wm.ex.ToolWindowManagerListener
import com.think1024.tocodesign.ideaplugin.constants.WindowIds
import com.think1024.tocodesign.ideaplugin.services.CodeGenerationService
import com.think1024.tocodesign.ideaplugin.services.NotificationPollingService
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.webview.TocoBrowserManager

class TocoPluginToolWindowFactory : ToolWindowFactory, DumbAware {
  override fun createToolWindowContent(project: Project, toolWindow: ToolWindow) {
    val browser = TocoBrowserManager.getInstance(project).getBrowser(toolWindow.id)
    val content = ContentFactory.getInstance().createContent(browser.getComponent(), null, false)
    toolWindow.contentManager.addContent(content)

    // 获取代码生成服务
    val codeGenerationService = CodeGenerationService.getInstance(project)

    // 添加标题栏操作
    val titleActions = mutableListOf<AnAction>()
    when (toolWindow.id) {
      WindowIds.TOCO_MENU -> {
        createTitleActions(titleActions, "TocoMenu.TitleBarActions")
      }
      WindowIds.TOCO_DESIGN -> {
        createTitleActions(titleActions, "TocoDesign.TitleBarActions")

        // 获取已经启动的轮询服务实例（在ProjectOpenStartupActivity中启动）
        val pollingService = NotificationPollingService.getInstance(project)

        // 添加工具窗口管理器监听器，监听工具窗口的展开收起状态
        project.messageBus.connect().subscribe(ToolWindowManagerListener.TOPIC, object : ToolWindowManagerListener {
          override fun stateChanged(toolWindowManager: ToolWindowManager) {
            val tocoToolWindow = toolWindowManager.getToolWindow(WindowIds.TOCO_DESIGN)
            if (tocoToolWindow != null && tocoToolWindow.isVisible) {
              // 工具窗口变为可见时清除红点
              pollingService.clearNotificationDot()
            }
          }
        })

        // 添加内容管理器监听器，当用户激活工具窗口时清除红点
        toolWindow.contentManager.addContentManagerListener(object : ContentManagerListener {
          override fun contentAdded(event: ContentManagerEvent) {}
          override fun contentRemoved(event: ContentManagerEvent) {}
          override fun contentRemoveQuery(event: ContentManagerEvent) {}
          override fun selectionChanged(event: ContentManagerEvent) {
            // 当工具窗口被激活时清除红点
            pollingService.clearNotificationDot()
          }
        })

        if (project.basePath != null) {
          // 应用代码
          browser.getBrowser().addEventListener("apply-code") { config ->
            codeGenerationService.handleApplyCode(browser.getBrowser(), config)
          }
          browser.getBrowser().addEventListener("apply-all-codes") { body ->
            codeGenerationService.handleApplyAllCodes(browser.getBrowser(), body.get("files").asJsonArray)
          }
        }
      }
      WindowIds.TOCO_BUILD -> createTitleActions(titleActions, "TocoBuild.TitleBarActions")
    }

    // 添加最大化操作
    val maximizeAction = ActionManager.getInstance().getAction("MaximizeToolWindow")
    if (maximizeAction != null) {
      titleActions.add(maximizeAction)
    }

    toolWindow.setTitleActions(titleActions)
  }

  private fun createTitleActions(titleActions: MutableList<in AnAction>, groupId: String) {
    val action = ActionManager.getInstance().getAction(groupId)
    if (action != null) {
      titleActions.add(action)
    }
  }

  override fun shouldBeAvailable(project: Project) = ProjectPluginSettings.getInstance(project).projectId != null
}
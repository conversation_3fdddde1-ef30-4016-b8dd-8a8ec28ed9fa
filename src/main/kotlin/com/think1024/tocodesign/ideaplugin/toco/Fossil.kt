package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileTypes.FileTypeRegistry
import com.intellij.openapi.project.Project
import com.intellij.openapi.application.ApplicationManager
import com.intellij.psi.PsiFileFactory
import com.intellij.psi.codeStyle.CodeStyleManager
import com.think1024.tocodesign.ideaplugin.utils.*
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import org.apache.commons.io.FileUtils
import java.io.File
import java.io.IOException
import java.nio.file.Paths
import java.util.*


data class FossilResult(
    val success: Boolean,
    val data: Any? = null,
    val message: String? = null
)

data class Item(
    val uuid: String,
    val name: String
)

/**
 * Description of a code file to be generated.
 */
data class CodeDescription(
    /** The code content. */
    val code: String,
    /** The relative path of the file. */
    val path: String,
    /** The full path of the file. */
    val fullPath: String,
    /** Whether to overwrite existing files. */
    val overwrite: Boolean,
    /** The module name, if applicable. */
    val moduleName: String? = null,
    /** The group name, if applicable. */
    val groupName: String? = null,
    /** The artifact name, if applicable. */
    val artifactName: String? = null
)

/**
 * Result of a code generation request.
 */
data class CodeGenerateResult(
    /** The module ID. */
    val moduleId: String,
    /** The module name. */
    val moduleName: String,
    /** Optional response containing module and code file details. */
    val codeFileList: List<CodeDescription>?
)

/**
 * Result of generating code, including optional abort and finish operations.
 */
data class CodeGenerateFossilResult(
    val success: Boolean,
    val message: String? = null,
    val data: List<MergeFile>? = null,
    val abort: (() -> Unit)? = null,
    val finish: ((recover: Boolean) -> CodeFinishResult)? = null
)

/**
 * Result of finishing the code generation process.
 */
data class CodeFinishResult(
    val success: Boolean,
    val message: String? = null
)

// TODO: i18n
object Fossil {
    private val logger = Logger.getInstance(Fossil::class.java)
    private const val INITIAL_COMMENT = "init fossil"
    private const val INITIAL_PROJECT_COMMENT = "init project"
    private const val KEEP_FILE_NAME = ".fossilkeep"

    /**
     * 获取项目目录路径
     */
    private fun getProjectsDir(): String {
        return Paths.get(PathUtil.getPluginDataDir().toString(), "projects").toString()
    }

    /**
     * 获取项目文件路径
     */
    fun getProjectFile(projectName: String): String {
        return Paths.get(getProjectsDir(), projectName, "$projectName.fossil").toString()
    }

    /**
     * userData中的纯生成代码模块分支文件夹
     */
    fun getModuleBranchFolder(projectName: String, moduleName: String): String {
        return Paths.get(getProjectsDir(), projectName, "branches", moduleName).toString()
    }

    /**
     * 指定目录下的模块文件夹
     */
    fun getModuleWorkFolder(projectWorkFolder: String, moduleName: String = ""): String {
        return Paths.get(projectWorkFolder, FossilCmd.MODULE_FOLDER_NAME, moduleName).toString()
    }

    fun format(code: String, project: Project): String {
        try {
            // Create a temporary PSI file from the code string
            val fileName = "temp.java"
            val type = FileTypeRegistry.getInstance().getFileTypeByFileName(fileName)

            // Format the PSI file using CodeStyleManager
            var formattedText = code
            ApplicationManager.getApplication().runReadAction({
                val psiFile = PsiFileFactory.getInstance(project)
                    .createFileFromText(fileName, type, code, 0, false, false)

                val codeStyleManager = CodeStyleManager.getInstance(project)
                codeStyleManager.reformat(psiFile)
                formattedText = psiFile.text
            })

            // Return the formatted code
            return formattedText
        } catch (e: Exception) {
            logger.error("[format]: Failed to format Java code: ${e.message}")
            throw e // Let caller handle the error
        }
    }

    /**
     * 创建项目
     */
    fun createProject(project: Item): FossilResult {
        val projectFilePath = FossilCmd.createRepo(project.name, getProjectsDir())
        return if (projectFilePath != null) {
            FossilResult(success = true)
        } else {
            FossilResult(success = false, message = "创建项目失败")
        }
    }

    /**
     * 打开Fossil仓库
     */
    private fun openFossilRepo(project: Item, projectWorkFolder: String): Boolean {
        val projectFile = getProjectFile(project.name)
        if (!File(projectFile).exists()) {
            val createResult = createProject(project)
            if (!createResult.success) {
                return false
            }
        }
        FossilCmd.cleanDbJournalFile(projectWorkFolder)
        return FossilCmd.openRepo(projectFile, projectWorkFolder)
    }

    /**
     * 初始化项目
     */
    fun initProject(
        project: Item,
        projectWorkFolder: String,
        onRenderProject: (project: Item, projectWorkFolder: String) -> FossilResult
    ): FossilResult {
        // 新建工作区目录
        try {
            File(projectWorkFolder).mkdirs()
        } catch (e: Exception) {
            return FossilResult(
                success = false,
                message = "初始化项目失败：${e.message}"
            )
        }

        FossilCmd.cleanDbConnectFile(projectWorkFolder)

        // fossil open
        val opened = openFossilRepo(project, projectWorkFolder)
        if (!opened) {
            return FossilResult(
                success = false,
                message = "初始化项目失败：打开项目仓库失败"
            )
        }

        val isEmpty = FileUtil.isEmptyDirectory(projectWorkFolder)
        if (isEmpty) {
            // 如果是空目录直接update
            val success = FossilCmd.updateRepo(projectWorkFolder)
            if (!success) {
                return FossilResult(
                    success = false,
                    message = "初始化项目失败：clone仓库失败"
                )
            }
        }

        // 检查是否为空仓库
        val latest = FossilCmd.getLatestCommit(projectWorkFolder)
            ?: return FossilResult(
                success = false,
                message = "初始化项目失败：获取提交信息失败"
            )

        if (FossilCmd.isRepoEmpty(latest)) {
            // 空仓库需要创建.fossilkeep并初次提交

            // touch .fossilkeep
            val keepFilePath = Paths.get(projectWorkFolder, KEEP_FILE_NAME).toString()
            val keepFile = File(keepFilePath)
            if (!keepFile.exists()) {
                keepFile.writeText("")
            }

            // 提交initial commit
            val success = FossilCmd.addFile(
                KEEP_FILE_NAME,
                projectWorkFolder,
                INITIAL_COMMENT,
                bot = true
            )
            if (success == null) {
                return FossilResult(
                    success = false,
                    message = "初始化项目失败：提交失败"
                )
            }
        } else {
            // 如果不是空仓库，最后一次提交必须是 trunk的init fossil
            if (latest.branch != "trunk" || latest.comment != INITIAL_COMMENT) {
                // 重复初始化
                return FossilResult(
                    success = false,
                    message = "初始化项目失败：仓库不为空"
                )
            }
        }

        var restore: ((folder: String?) -> Unit)? = null
        // 如果文件夹内此时已经存在文件，需要检查是否projectId匹配，如果不匹配需要渲染
        // 在复原fossil的情况下，不需要渲染
        val projectInfo = ProjectConfigUtil.getProjectInfo(projectWorkFolder)
        if (projectInfo?.get("id") != project.uuid) {
            // 创建回退点
            restore = FossilCmd.archive(projectWorkFolder)
            if (restore == null) {
                return FossilResult(
                    success = false,
                    message = "初始化项目失败：创建存档点失败"
                )
            }

            // 渲染工程
            val result = onRenderProject(project, projectWorkFolder)
            if (!result.success) {
                restore(null)
                return FossilResult(
                    success = false,
                    message = result.message ?: "初始化项目失败：渲染工程失败"
                )
            }
        }

        // 提交工程
        // 排除掉modules提交
        val commitId = FossilCmd.commitFiles(
            projectWorkFolder,
            INITIAL_PROJECT_COMMENT,
            bot = true,
            listOf("modules/*")
        )
        if (commitId == null) {
            restore?.let { it(null) }
            return FossilResult(
                success = false,
                message = "初始化项目失败：提交失败"
            )
        }
        val clean = FossilCmd.isRepoClean(projectWorkFolder)
        if (!clean) {
            // 提交modules
            FossilCmd.commitFiles(
                projectWorkFolder,
                "commit modules",
                bot = true,
            )
        }
        return FossilResult(success = true, data = project.uuid)
    }

    fun createModule(
        project: Item,
        module: Item,
        projectWorkFolder: String,
        onRenderModule: (project: Item, module: Item, moduleFolder: String) -> FossilResult,
        onSuccess: () -> Unit
    ): FossilResult {
        val moduleFolder = getModuleBranchFolder(project.name, module.name)
        try {
            File(moduleFolder).mkdirs()
        } catch (e: IOException) {
            return FossilResult(
                success = false,
                message = "新建模块失败：${e.message}"
            )
        }

        // Fossil open
        val branchOpened = openFossilRepo(project, moduleFolder)
        if (!branchOpened) {
            return FossilResult(
                success = false,
                message = "新建模块失败：打开分支仓库失败"
            )
        }

        val currentBranch = FossilCmd.getCurrentBranch(moduleFolder)
            ?: return FossilResult(
                success = false,
                message = "新建模块失败：获取当前分支失败"
            )

        val initModuleComment = "init module"
        if (currentBranch != module.name) {
            val branchExist = FossilCmd.isBranchExist(module.name, moduleFolder)
            if (!branchExist) {
                // Create module branch at initial project commit
                val initialProjectId = FossilCmd.getCommitIdWithComment(INITIAL_PROJECT_COMMENT, moduleFolder)
                    ?: return FossilResult(
                        success = false,
                        message = "新建模块失败：获取initial project提交失败"
                    )
                val success = FossilCmd.newBranch(module.name, moduleFolder, initialProjectId)
                if (!success) {
                    return FossilResult(
                        success = false,
                        message = "新建模块失败：创建分支失败"
                    )
                }
            }
            // Switch to new branch
            val success = FossilCmd.switchToBranch(module.name, moduleFolder)
            if (!success) {
                return FossilResult(
                    success = false,
                    message = "新建模块失败：切换分支失败"
                )
            }
        }

        // Create restore point
        val restoreModule = FossilCmd.archive(moduleFolder)
            ?: return FossilResult(
                success = false,
                message = "新建模块失败：创建分支存档点失败"
            )

        val isEmpty = FileUtil.isEmptyDirectory(moduleFolder, listOf(KEEP_FILE_NAME, FossilCmd.getOpenMarkFile()))
        if (isEmpty) {
            // Update empty repository
            val success = FossilCmd.updateRepo(moduleFolder)
            if (!success) {
                restoreModule(null)
                return FossilResult(
                    success = false,
                    message = "新建模块失败：clone仓库失败"
                )
            }
        }

        // Check for existing init module commit
        var initModuleId = FossilCmd.getCommitIdWithComment(initModuleComment, moduleFolder)
        val moduleBranchDir = FileUtil.getFile(getModuleWorkFolder(moduleFolder, module.name))
        if (initModuleId == null || moduleBranchDir == null) {
            // Render module
            val moduleInfo = ProjectConfigUtil.getModuleInfo(getModuleWorkFolder(moduleFolder, module.name))
            if (moduleInfo?.get("id") != module.uuid) {
                val result = onRenderModule(project, module, moduleFolder)
                if (!result.success) {
                    restoreModule(null)
                    return FossilResult(
                        success = false,
                        message = result.message ?: "新建模块失败：渲染模块失败"
                    )
                }
            }

            // Commit module code
            initModuleId = FossilCmd.commitFiles(moduleFolder, initModuleComment, true)
            if (initModuleId == null) {
                restoreModule(null)
                return FossilResult(
                    success = false,
                    message = "新建模块失败：提交失败"
                )
            }
        }

        val projectOpened = openFossilRepo(project, projectWorkFolder)
        if (!projectOpened) {
            restoreModule(null)
            return FossilResult(
                success = false,
                message = "新建模块失败：打开项目仓库失败"
            )
        }

        abortMergeIfNeeded(projectWorkFolder)

        // Create restore point for trunk
        val restoreTrunk = FossilCmd.archive(projectWorkFolder)
        if (restoreTrunk == null) {
            restoreModule(null)
            return FossilResult(
                success = false,
                message = "新建模块失败：创建主干存档点失败"
            )
        }

        val mergeResult = FossilCmd.mergeCommit(projectWorkFolder, initModuleId, module.name)
        if (mergeResult.status == MergeResultStatus.Conflict) {
            val conflictFiles = mergeResult.files?.filter { it.type == MergeStatus.UNMERGED } ?: emptyList()
            val conflictPoms = conflictFiles.filter { Paths.get(it.mergedPath).fileName.toString()
                .lowercase(Locale.getDefault()) == "pom.xml" }
            if (conflictPoms.isNotEmpty()) {
                val isAllPom = conflictPoms.size == conflictFiles.size
                var success = true
                for (file in conflictPoms) {
                    val merged = PomMerger.mergePomFiles(file.oursContent, file.theirsContent)
                    if (merged.isNullOrEmpty()) {
                        mergeResult.status = MergeResultStatus.Failed
                        success = false
                    } else {
                        val pomFile = FileUtil.getFile(Paths.get(projectWorkFolder, file.mergedPath).toString())
                        pomFile?.writeText(merged)
                        file.mergedContent = merged
                        file.type = MergeStatus.MODIFIED
                    }
                }
                if (success && isAllPom) {
                    mergeResult.status = MergeResultStatus.Success
                }
            }
        }
        if (mergeResult.status != MergeResultStatus.Success) {
            restoreTrunk(null)
            restoreModule(null)
            return FossilResult(
                success = false,
                message = "新建模块失败：合并分支失败"
            )
        }

        if (mergeResult.files?.isNotEmpty() == true) {
            val commitId = FossilCmd.doCommit(projectWorkFolder, "merge module after create module", true)
            if (commitId == null) {
                restoreTrunk(null)
                restoreModule(null)
                return FossilResult(
                    success = false,
                    message = "新建模块失败：提交失败"
                )
            }
        }

        onSuccess()
        return FossilResult(success = true)
    }

    fun cleanModule(
        project: Item,
        projectWorkFolder: String,
        moduleName: String,
    ): FossilResult {
        val moduleFolder = getModuleBranchFolder(project.name, moduleName)
        val dir = FileUtil.getFile(getModuleWorkFolder(moduleFolder, moduleName))
        if (dir == null) {
            return FossilResult(success = true)
        }

        val branchOpened = openFossilRepo(project, moduleFolder)
        if (!branchOpened) {
            return FossilResult(
                success = false,
                message = "清理模块代码失败：打开分支仓库失败"
            )
        }

        // Create restore point
        var restoreModule = FossilCmd.archive(moduleFolder)
            ?: return FossilResult(
                success = false,
                message = "清理模块代码失败：创建分支存档点失败"
            )

       try {
           FileUtils.deleteDirectory(dir)
       } catch (_: Exception) {
           restoreModule(null)
           return FossilResult(
               success = false,
               message = "清理模块代码失败：删除文件夹失败"
           )
       }

        val pomFile = FileUtil.getFile(Paths.get(moduleFolder, "pom.xml").toString())
        if (pomFile != null) {
            val xml = PomMerger.removeModule(pomFile.readText(), moduleName)
            if (xml.isNotEmpty()) {
                pomFile.writeText(xml)
            }
        }


        val toMergeCommitId = FossilCmd.commitFiles(moduleFolder, "delete module $moduleName files", true)
        if (toMergeCommitId == null) {
            restoreModule(null)
            return FossilResult(
                success = false,
                message = "清理模块代码失败：提交模块分支代码失败"
            )
        }

        // Merge module branch to trunk
        val projectOpened = openFossilRepo(project, projectWorkFolder)
        if (!projectOpened) {
            restoreModule(null)
            return FossilResult(
                success = false,
                message = "清理模块代码失败：打开项目仓库失败"
            )
        }

        abortMergeIfNeeded(projectWorkFolder)

        // Create restore point for trunk
        val restoreTrunk = FossilCmd.archive(projectWorkFolder, "commit before clean $moduleName code")
        if (restoreTrunk == null) {
            restoreModule(null)
            return FossilResult(
                success = false,
                message = "清理模块代码失败：创建主干存档点失败"
            )
        }

        val mergeResult = FossilCmd.mergeCommit(projectWorkFolder, toMergeCommitId, moduleName)
        if (mergeResult.status == MergeResultStatus.Conflict) {
            val conflictFiles = mergeResult.files?.filter { it.type == MergeStatus.UNMERGED } ?: emptyList()
            val conflictPoms = conflictFiles.filter { it.mergedPath == "pom.xml" }
            if (conflictPoms.isNotEmpty()) {
                val isAllPom = conflictPoms.size == conflictFiles.size
                var success = true
                for (file in conflictPoms) {
                    val xml = PomMerger.removeModule(file.oursContent, moduleName)
                    if (xml.isEmpty()) {
                        mergeResult.status = MergeResultStatus.Failed
                        success = false
                    } else {
                        val pomFile = FileUtil.getFile(Paths.get(projectWorkFolder, file.mergedPath).toString())
                        pomFile?.writeText(xml)
                        file.mergedContent = xml
                        file.type = MergeStatus.MODIFIED
                    }
                }
                if (success && isAllPom) {
                    mergeResult.status = MergeResultStatus.Success
                }
            }
        }
        if (mergeResult.status != MergeResultStatus.Success) {
            restoreTrunk(null)
            restoreModule(null)
            return FossilResult(
                success = false,
                message = "清理模块代码失败：合并分支失败"
            )
        }

        val commitId = FossilCmd.doCommit(projectWorkFolder, "merge module after clean module", true)
        if (commitId == null) {
            restoreTrunk(null)
            restoreModule(null)
            CodeFinishResult(
                success = false,
                message = "清理模块代码失败：提交失败"
            )
        }

        return FossilResult(success = true)
    }

    /**
     * 删除模块
     */
    fun removeModule(
        project: Item,
        projectWorkFolder: String,
        moduleName: String,
        onSuccess: () -> Unit
    ): FossilResult {
        val moduleFolder = getModuleBranchFolder(project.name, moduleName)
        val branchOpened = openFossilRepo(project, moduleFolder)

        if (!branchOpened) {
            return FossilResult(
                success = false,
                message = "删除模块失败：打开分支仓库失败"
            )
        }

        // 删除模块分支
        var success = FossilCmd.removeBranch(moduleName, moduleFolder)
        if (!success) {
            return FossilResult(
                success = false,
                message = "删除模块失败：删除分支失败"
            )
        }

        // 关闭模块分支
        FossilCmd.closeRepo(getProjectFile(project.name), moduleFolder)
        // 删除模块分支目录
        try {
            val dir = FileUtil.getFile(moduleFolder)
            if (dir != null) {
                FileUtils.deleteDirectory(dir)
            }
            success = true
        } catch (_: Exception) {
            success = false
        }
        if (!success) {
            return FossilResult(
                success = false,
                message = "删除模块失败：删除模块分支目录失败"
            )
        }

        val projectOpened = openFossilRepo(project, projectWorkFolder)
        if (!projectOpened) {
            return FossilResult(
                success = false,
                message = "删除模块失败：打开项目仓库失败"
            )
        }

        // 创建回退点
        val restoreTrunk = FossilCmd.archive(projectWorkFolder)
            ?: return FossilResult(
                success = false,
                message = "删除模块失败：创建存档点失败"
            )

        // 工作目录（主干）删除当前模块代码
        try {
            val dir = FileUtil.getFile(getModuleWorkFolder(projectWorkFolder, moduleName))
            if (dir != null) {
                FileUtils.deleteDirectory(dir)
            }
            // 删除pom中的模块
            val pomFile = FileUtil.getFile(Paths.get(projectWorkFolder, "pom.xml").toString())
            if (pomFile != null) {
                val xml = PomMerger.removeModule(pomFile.readText(), moduleName)
                if (xml.isNotEmpty()) {
                    pomFile.writeText(xml)
                }
            }
            success = true
        } catch (_: Exception) {
            success = false
        }
        if (!success) {
            restoreTrunk(null)
            return FossilResult(
                success = false,
                message = "删除模块失败：删除模块代码失败"
            )
        }

        val commitId = FossilCmd.commitFiles(
            projectWorkFolder,
            "delete module $moduleName",
            true
        )
        if (commitId == null) {
            restoreTrunk(null)
            return FossilResult(
                success = false,
                message = "删除模块失败：提交失败"
            )
        }

        onSuccess()
        return FossilResult(success = true)
    }

    fun generateCode(
        project: Item,
        projectWorkFolder: String,
        code: CodeGenerateResult,
        ignoreConflict: Boolean?,
        stateCallback: (state: String, writeFileProgress: Double, diffFileProgress: Double) -> Unit,
        onSuccess: () -> Unit,
    ): CodeGenerateFossilResult {
        val codes = code.codeFileList ?: emptyList()
        val moduleFolder = getModuleBranchFolder(project.name, code.moduleName)

        // Fossil open
        val branchOpened = openFossilRepo(project, moduleFolder)
        if (!branchOpened) {
            return CodeGenerateFossilResult(
                success = false,
                message = "生成代码失败：打开分支仓库失败"
            )
        }

        // Create restore point
        var restoreModule = FossilCmd.archive(moduleFolder)
            ?: return CodeGenerateFossilResult(
                success = false,
                message = "生成代码失败：创建分支存档点失败"
            )

        // Clean module code
        stateCallback(getI18nString("code.generate.clean.module"), 0.0, 0.0)
        val folder = getModuleWorkFolder(moduleFolder, code.moduleName)
        val files = FileUtil.readDirectoryRecursive(folder)
        for ((filePath, type) in files) {
            if (type == FileType.FILE && FossilCmd.isJava(filePath)) {
                val file = FileUtil.getFile(filePath)
                file?.delete()
            }
        }

        // Write new code
        stateCallback(getI18nString("code.generate.write.module.code"), 0.0, 0.0)
        val codeMap: MutableMap<String, String> = mutableMapOf()
        var failed = false
        var count = 0
        for (codeFile in codes) {
            try {
                val filepath = Paths.get(getModuleWorkFolder(moduleFolder), codeFile.fullPath).toString()
                val file = FileUtil.getFile(filepath, true)
                file?.writeText(codeFile.code)
                codeMap[Paths.get(FossilCmd.MODULE_FOLDER_NAME, codeFile.fullPath).toString()] = codeFile.code
                count++
                stateCallback(getI18nString("code.generate.write.module.code"), count * 1.0 / codes.size, 0.0)
            } catch (e: Exception) {
                logger.error("[generateCode]: Failed to write file ${codeFile.fullPath}: ${e.message}")
                failed = true
                break
            }
        }
        if (failed) {
            restoreModule(null)
            return CodeGenerateFossilResult(
                success = false,
                message = "生成代码失败：写入代码失败"
            )
        }

//        stateCallback(getI18nString("code.generate.commit.module.code"), 1.0, 0.0)
        val toMergeCommitId = FossilCmd.commitFiles(moduleFolder, "generate ${code.moduleName} code", true)
        if (toMergeCommitId == null) {
            restoreModule(null)
            return CodeGenerateFossilResult(
                success = false,
                message = "生成代码失败：提交模块分支代码失败"
            )
        }

        restoreModule = FossilCmd.archive(moduleFolder)
            ?: return CodeGenerateFossilResult(
                success = false,
                message = "生成代码失败：创建分支存档点失败"
            )

        // Merge module branch to trunk
        val projectOpened = openFossilRepo(project, projectWorkFolder)
        if (!projectOpened) {
            restoreModule(null)
            return CodeGenerateFossilResult(
                success = false,
                message = "生成代码失败：打开项目仓库失败"
            )
        }

        abortMergeIfNeeded(projectWorkFolder)

        // Create restore point for trunk
        val restoreTrunk = FossilCmd.archive(projectWorkFolder, "commit before generate ${code.moduleName} code")
        if (restoreTrunk == null) {
            restoreModule(null)
            return CodeGenerateFossilResult(
                success = false,
                message = "生成代码失败：创建主干存档点失败"
            )
        }

        stateCallback(getI18nString("code.generate.merge.module.code"), 1.0, 0.0)
        val mergeResult = FossilCmd.mergeCommit(projectWorkFolder, toMergeCommitId, code.moduleName, ignoreConflict, codeMap)
        if (mergeResult.status == MergeResultStatus.Failed) {
            restoreTrunk(null)
            restoreModule(null)
            return CodeGenerateFossilResult(
                success = false,
                message = "生成代码失败：合并分支失败"
            )
        }

        stateCallback(getI18nString("code.generate.merge.module.code"), 1.0, 0.9)

        val abort: () -> Unit = { ->
            val clean = FossilCmd.isRepoClean(projectWorkFolder)
            if (!clean) {
                restoreTrunk(null)
                restoreModule(null)
            }
        }

        val finish: (Boolean) -> CodeFinishResult = { recover ->
            val commitId = FossilCmd.doCommit(projectWorkFolder, "merge module after code generation", true)
            if (!recover) {
                stateCallback(getI18nString("code.generate.merge.module.code"), 1.0, 1.0)
            }
            if (commitId == null) {
                restoreTrunk(null)
                restoreModule(null)
                CodeFinishResult(
                    success = false,
                    message = "生成代码失败：取消冲突解决"
                )
            } else {
                if (recover) {
                    val recoverCommitId = FossilCmd.commitFiles(projectWorkFolder, "recover files", true)
                    stateCallback(getI18nString("code.generate.merge.module.code"), 1.0, 1.0)
                    if (recoverCommitId == null) {
                        restoreTrunk(null)
                        restoreModule(null)
                        CodeFinishResult(
                            success = false,
                            message = "生成代码失败：恢复文件失败"
                        )
                    } else {
                        FossilCmd.clean(moduleFolder)
                        FossilCmd.clean(projectWorkFolder)
                        onSuccess()
                        CodeFinishResult(success = true)
                    }
                } else {
                    FossilCmd.clean(moduleFolder)
                    FossilCmd.clean(projectWorkFolder)
                    onSuccess()
                    CodeFinishResult(success = true)
                }
            }
        }

        return CodeGenerateFossilResult(
            success = true,
            data = mergeResult.files ?: listOf(),
            abort = abort,
            finish = finish
        )
    }



    fun fixFossilAdmin(projectWorkFolder: String) {
        FossilCmd.fixFossilAdmin(projectWorkFolder)
    }

    fun abortMergeIfNeeded(projectWorkFolder: String) {
        val status = FossilCmd.status(projectWorkFolder)
        if (status.contains("MERGED_WITH")) {
            FossilCmd.revert(projectWorkFolder)
        }
    }

    /**
     * 打开项目UI
     */
    fun openUI(project: Item, projectWorkFolder: String): FossilResult {
        val projectOpened = FossilCmd.openRepo(
            getProjectFile(project.name),
            projectWorkFolder
        )
        if (!projectOpened) {
            return FossilResult(
                success = false,
                message = "打开fossil界面失败：打开项目仓库失败"
            )
        }

        return try {
            FossilCmd.openRepoUI(projectWorkFolder)
            FossilResult(success = true)
        } catch (e: Exception) {
            FossilResult(
                success = false,
                message = e.message
            )
        }
    }

    /**
     * 清理Fossil相关文件
     */
    fun cleanFossil(project: Item, projectWorkFolder: String): Boolean {
        try {
            FossilCmd.closeRepoUI()
            // 删除.fslckout和.fossilkeep
            FossilCmd.cleanDbConnectFile(projectWorkFolder)
            FileUtil.getFile(Paths.get(projectWorkFolder, KEEP_FILE_NAME).toString())?.delete()
            // 删除分支目录
            val dir = FileUtil.getFile(Paths.get(getProjectsDir(), project.name).toString())
            if (dir != null) {
                FileUtils.deleteDirectory(dir)
            }
            return true
        } catch (_: Exception) {
            return false
        }
    }

    /**
     * 清理共同祖先为初始项目提交的分支
     */
    fun cleanupBranchesWithInitialProjectAncestor(workingFolder: String, projectName: String): List<String> {
        val deletedBranches = mutableListOf<String>()

        try {
            val allBranches = FossilCmd.getAllBranches(workingFolder)
            val currentBranch = FossilCmd.getCurrentBranch(workingFolder)

            // 获取初始项目提交的ID
            val initialProjectCommitId = FossilCmd.getCommitIdWithComment(INITIAL_PROJECT_COMMENT, workingFolder)
            if (initialProjectCommitId == null) {
                logger.warn("[cleanupBranchesWithInitialProjectAncestor]: 未找到初始项目提交")
                return emptyList()
            }

            // 如果当前分支需要被删除，先切换到trunk
            if (currentBranch != null && currentBranch != "trunk" &&
                shouldDeleteBranchByInitialAncestor(currentBranch, initialProjectCommitId, workingFolder)) {
                val switchSuccess = FossilCmd.switchToBranch("trunk", workingFolder)
                if (!switchSuccess) {
                    logger.warn("[cleanupBranchesWithInitialProjectAncestor]: 切换到trunk失败")
                    return emptyList()
                }
            }

            allBranches.forEach { branch ->
                if (branch != "trunk" && shouldDeleteBranchByInitialAncestor(branch, initialProjectCommitId, workingFolder)) {
                    if (FossilCmd.removeBranch(branch, workingFolder)) {
                        deletedBranches.add(branch)
                        // 删除分支目录
                        val branchDir = FileUtil.getFile(getModuleBranchFolder(projectName, branch))
                        FileUtils.deleteDirectory(branchDir)

                        logger.info("[cleanupBranchesWithInitialProjectAncestor]: 删除分支 $branch")
                    } else {
                        logger.warn("[cleanupBranchesWithInitialProjectAncestor]: 删除分支 $branch 失败")
                    }
                }
            }
        } catch (e: Exception) {
            logger.error("[cleanupBranchesWithInitialProjectAncestor]: ${e.message}")
        }

        return deletedBranches
    }

    /**
     * 判断分支是否应该被删除（基于初始项目提交作为共同祖先）
     */
    private fun shouldDeleteBranchByInitialAncestor(
        branchName: String,
        initialProjectCommitId: String,
        workingFolder: String
    ): Boolean {
        return try {
            // 找到分支与trunk的共同祖先
            val commonAncestor = FossilCmd.findMergeBase("trunk", branchName, workingFolder)

            // 如果共同祖先就是初始项目提交，则删除该分支
            commonAncestor == initialProjectCommitId
        } catch (e: Exception) {
            logger.error("[shouldDeleteBranchByInitialAncestor]: 检查分支 $branchName 时出错 - ${e.message}")
            false
        }
    }
}
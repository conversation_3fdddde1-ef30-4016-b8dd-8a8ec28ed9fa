package com.think1024.tocodesign.ideaplugin.services.locator

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.ModalityState
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileTypes.FileTypeManager
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.ProgressManager
import com.intellij.openapi.progress.Task
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.io.FileUtil
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.VirtualFileManager
import com.intellij.psi.*
import com.intellij.psi.search.FilenameIndex
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.psi.search.GlobalSearchScopes
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.ConfigUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import com.think1024.tocodesign.ideaplugin.utils.ModuleConfigUtil
import com.think1024.tocodesign.ideaplugin.utils.ReadActionUtil.runReadAction
import java.io.File
import java.nio.file.Paths
import java.util.*

/**
 * Interface for locator rule implementations.
 * This interface provides a standardized way to generate locator requests, retrieve project and module information,
 * and perform file location and navigation operations within an IntelliJ IDEA plugin.
 */
interface ILocatorRuler {
    companion object {
        /**
         * Constant for the separator used in locator strings.
         */
        const val LOCATOR_IDX_SEPARATOR = "|"

        /**
         * Constant for the separator used in type-to-path mappings.
         */
        const val TYPE_TO_PATH_SEPARATOR = "|"

        /**
         * Constant for the character used to split package paths.
         */
        const val PACKAGE_PATH_SPLITTER = '.'

        /**
         * Constant for the character used to split directory paths.
         */
        val DIR_PATH_SPLITTER: String = File.separator
    }

    /**
     * Logger instance for logging operations within the locator.
     */
    val logger: Logger

    /**
     * Mapping of type strings to their corresponding file path patterns.
     * The key represents the type, and the value is a string containing submodule path and file path separated by '|'.
     */
    val typeToPathMapping: Map<String, String>

    /**
     * The current project context.
     */
    val project: Project

    /**
     * LocatorBuilder instance for constructing locator-related objects.
     */
    val locatorBuilder: LocatorBuilder
        get() = LocatorBuilder(project)

    /**
     * The file extension for locator files, retrieved from configuration.
     */
    val locatorFileExtension: String
        get() = ConfigUtil.getProperty("locator.file.extension")

    /**
     * The source path for locator files, retrieved from configuration.
     */
    val locatorSourcePath: String
        get() = ConfigUtil.getProperty("locator.source.path")

    val defaultAnnotation: String
        get() = ConfigUtil.getProperty("locator.default.annotation")
    /**
     * The fully qualified name of the annotation used to mark locator methods.
     */
    val locatorMethodAnnotation: String
        get() = ConfigUtil.getProperty("locator.method.annotation")

    /**
     * Retrieves the fully qualified name of the annotation used to mark public interfaces.
     *
     * This property dynamically fetches the value from the configuration using the key
     * "locator.public.interface.annotation". It's used to identify methods that are
     * part of the public API.
     *
     * @return The fully qualified name of the public interface annotation as a String.
     */
    val locatorPublicInterfaceAnnotation: String
        get() = ConfigUtil.getProperty("locator.public.interface.annotation")

    /**
     * Retrieves the fully qualified name of the annotation used to mark controller classes.
     *
     * This property dynamically fetches the value from the configuration using the key
     * "locator.controller.annotation". It's used to identify classes that serve as
     * controllers in the application architecture.
     *
     * @return The fully qualified name of the controller annotation as a String.
     */
    val locatorControllerAnnotation: String
        get() = ConfigUtil.getProperty("locator.controller.annotation")

    /**
     * The fully qualified name of the annotation used to mark locator classes.
     */
    val locatorClassAnnotation: String
        get() = ConfigUtil.getProperty("locator.class.annotation")

    /**
     * Data class representing the result of a locator operation.
     *
     * @property virtualFile The located virtual file.
     * @property psiClass The PSI class associated with the located file, if any.
     * @property psiMethod The PSI method associated with the located file, if any.
     */
    data class LocatorResult(
        val virtualFile: VirtualFile,
        val psiClass: PsiClass?,
        val psiMethod: PsiMethod?
    )

    /**
     * Matches the PsiFile based on the provided id and type.
     * Implementations should define the specific matching logic.
     *
     * @param psiFile The PSI file to check.
     * @param locatorInfo The locator information to match against.
     * @return LocatorResult if the PsiFile matches the given criteria, null otherwise.
     */
    fun matches(psiFile: PsiFile, locatorInfo: LocatorBuilder.LocatorInfo): LocatorResult?

    /**
     * Opens the target file and navigates to the specific code line.
     * Implementations should handle file opening and navigation logic.
     *
     * @param locatorResult The LocatorResult containing the target file and associated PSI elements.
     * @return True if the navigation is successful, false otherwise.
     * @throws Exception if there's an error during the navigation process.
     */
    @Throws(Exception::class)
    fun openAndNavigateToTarget(locatorResult: LocatorResult): Boolean

    /**
     * Builds a locator request object from a PsiElement (PsiMethod or PsiClass) with an AutoGenerated annotation.
     * This method extracts necessary information from the element and its annotation to create a locator request.
     *
     * @param element PsiElement to extract information from (must be a PsiMethod or PsiClass).
     * @param project Current project context.
     * @return LocatorInfo representing the locator request, or null if the annotation is not found or invalid.
     */
    fun buildRequest(element: PsiElement, project: Project): LocatorBuilder.LocatorInfo? {
        // Check if the element is valid before processing
        if (!element.isValid) {
            logger.warn(getI18nString("invalid.psi.element", element::class.java.simpleName))
            return null
        }

        // Use DumbService to check if the index is ready
        val dumbService = DumbService.getInstance(project)
        if (dumbService.isDumb) {
            logger.warn("Index is not ready, please wait...")
            return null
        }

        return runReadAction {
            // Ensure the element is a PsiMethod or PsiClass
            if (element !is PsiMethod && element !is PsiClass) return@runReadAction null

            // Get module ID and name
            val moduleId = getModuleId(element) ?: return@runReadAction null
            val moduleName = getModuleNameFromModule(element)

            // Find the AutoGenerated annotation
            val annotation = when (element) {
                is PsiMethod -> element.annotations.find { it.qualifiedName == locatorMethodAnnotation || it.qualifiedName == defaultAnnotation }
                is PsiClass -> element.annotations.find { it.qualifiedName == locatorClassAnnotation || it.qualifiedName == defaultAnnotation }
                else -> null
            } ?: return@runReadAction null

            val annotationPublicInterfaceInfo = when (element) {
                is PsiMethod -> {
                    // 1. 优先查找 PublicInterface 注解
                    element.annotations.find { it.qualifiedName == locatorPublicInterfaceAnnotation || it.qualifiedName == "PublicInterface" }
                }
                is PsiClass -> element.annotations.find { it.qualifiedName == locatorClassAnnotation || it.qualifiedName == defaultAnnotation }
                else -> null
            } ?: return@runReadAction null;

            // Extract UUID information from the annotation
            val locatorIndex = extractUuidInfo(annotation, annotationPublicInterfaceInfo)

            // Build and return the LocatorInfo
            locatorBuilder.buildLocatorInfo(locatorIndex, moduleId, moduleName)
        }
    }

    /**
     * Extracts the UUID information from the AutoGenerated annotation.
     * This method parses the UUID string and extracts the id, type, subType, name, searchKey and subLocatorType.
     *
     * @param annotation The AutoGenerated annotation containing the UUID.
     * @return LocatorIndex containing the id, type, subType, name, searchKey and subLocatorType extracted from the UUID.
     */
    fun extractUuidInfo(annotation: PsiAnnotation, annotationPublicInterfaceInfo: PsiAnnotation? = null): LocatorIndex {
        return runReadAction {
            // 先获取PublicInterface注解的id（可能为null）
            val uuidPublic = annotationPublicInterfaceInfo?.findAttributeValue("id")?.text?.trim('"')

            // 再获取AutoGenerated注解的uuid（可能为null）
            val uuidAuto = annotation.findAttributeValue("uuid")?.text?.trim('"')

            // 优先优先级：如果uuidPublic存在则使用它，否则使用uuidAuto
            val uuid = uuidPublic ?: uuidAuto

            // 最终检查：如果两者都为null则返回默认值
            if (uuid == null) {
                return@runReadAction LocatorIndex("", "", null, null, "", null)
            }

            // Split UUID into components
            val parts = uuid.split(LOCATOR_IDX_SEPARATOR)

            // Validate UUID format
            if (parts.isEmpty() || parts[0].isEmpty()) {
                logger.debug("Invalid UUID format: $uuid")
                return@runReadAction LocatorIndex("", "", null, null, "", null)
            }

            val id = parts[0]
            var type = if (parts.size > 1) parts[1] else ""
            val subType = if (parts.size > 2) parts[2] else null

            // Determine the containing element
            val containingElement = findContainingClassOrMethod(annotation)

            // Determine type based on context if not provided
            if (type.isBlank()) {
                val packageName = (containingElement?.containingFile as? PsiJavaFile)?.packageName ?: ""
                type = determineTypeFromPackage(packageName)

                if (type.isBlank()) {
                    type = determineInterfaceTypeFromAnnotations(containingElement)
                }
            }

            // Determine name based on containing element
            val name = when (containingElement) {
                is PsiClass -> containingElement.name
                is PsiMethod -> containingElement.name
                else -> null
            }

            // Determine the subLocatorType based on the containing element
            val subLocatorType = when (containingElement) {
                is PsiClass -> LocatorBuilder.SubLocatorType.CLASS.value
                is PsiMethod -> LocatorBuilder.SubLocatorType.METHOD.value
                else -> null
            }

            LocatorIndex(id, type, subType, name, uuid, subLocatorType)
        }
    }

    /**
     * Determines the interface type based on the annotations present on the containing method.
     *
     * @param containingElement The containing PsiMethod.
     * @return The determined type ("rpc" or "api") based on the annotations, or an empty string if not applicable.
     */
    private fun determineInterfaceTypeFromAnnotations(containingElement: PsiElement?): String {
        // Ensure the containing element is a PsiMethod; if not, return an empty string
        if (containingElement !is PsiMethod) {
            return ""
        }

        // Check if the element is valid before processing
        if (!containingElement.isValid) {
            logger.warn(getI18nString("invalid.psi.element", containingElement::class.java.simpleName))
            return ""
        }

        // Check if the method has the RPC or API annotations
        val isRpcOrApiAnnotationPresent =
            containingElement.annotations.any { it.qualifiedName == locatorMethodAnnotation || it.qualifiedName == defaultAnnotation } &&
                    containingElement.annotations.any { it.qualifiedName == locatorPublicInterfaceAnnotation }

        // Return an empty string if the required annotations are not present
        if (!isRpcOrApiAnnotationPresent) {
            return ""
        }

        // Get the containing class of the method
        val containingClass = containingElement.containingClass

        // Check if the containing class has the Controller annotation
        val isControllerAnnotationPresent = containingClass?.annotations?.any {
            it.qualifiedName == locatorControllerAnnotation
        } ?: false

        // Determine the type based on the presence of annotations
        return when {
            isControllerAnnotationPresent -> "api"
            else -> "rpc"
        }
    }

    /**
     * Determines the type based on the package name and typeToPathMapping.
     *
     * This function iterates through the typeToPathMapping entries to find a matching type
     * based on the given package name. It splits the pathInfo value and checks if the
     * package name ends with the second part of the split result (if it exists).
     *
     * @param packageName The full package name of the element to be matched.
     * @return The determined type as a String if a match is found, or an empty string if no match is found.
     */
    private fun determineTypeFromPackage(packageName: String): String {
        return typeToPathMapping.entries
            .firstOrNull { (_, pathInfo) ->
                // Split the pathInfo using TYPE_TO_PATH_SEPARATOR
                // Check if the second part (index 1) exists and if the packageName ends with it
                pathInfo.split(TYPE_TO_PATH_SEPARATOR).getOrNull(1)?.let { packageName.endsWith(it) } == true
            }?.key ?: "" // Return the key of the matching entry, or an empty string if no match is found
    }


    /**
     * Finds the containing class or method for the given annotation.
     * This helper method traverses up the PSI tree to find the nearest containing class or method.
     *
     * @param annotation The PsiAnnotation to find the container for.
     * @return The containing PsiClass or PsiMethod, or null if not found.
     */
    fun findContainingClassOrMethod(annotation: PsiAnnotation): PsiElement? {
        return runReadAction {
            var context = annotation.context
            while (context != null) {
                when (context) {
                    is PsiClass -> return@runReadAction context
                    is PsiMethod -> return@runReadAction context
                    else -> context = context.context
                }
            }
            null
        }
    }

    /**
     * Converts a camelCase string to snake_case.
     *
     * @param input The camelCase string to convert.
     * @return The converted snake_case string.
     */
    private fun camelToSnakeCase(input: String): String {
        return input.replace(Regex("([a-z])([A-Z])"), "\$1_\$2").lowercase(Locale.getDefault())
    }

    /**
     * Locates the file based on the provided locator info.
     * This method employs a three-step search strategy:
     * 1. Attempts to find the file using the search key from the LocatorIndexManager.
     * 2. If not found, attempts to find the file using a type-specific path.
     * 3. If still not found, falls back to searching the entire Maven source root.
     *
     * @param locatorInfo The locator info containing id, type, and module information.
     * @return The LocatorResult if found, null otherwise.
     */
    fun locateFile(locatorInfo: LocatorBuilder.LocatorInfo): LocatorResult? {
        // Step 1: Attempt to find the file using the search key from the LocatorIndexManager
        val searchKeyResult = locateFileBySearchKey(locatorInfo)
        if (searchKeyResult != null) {
            return searchKeyResult
        }

        // Step 2: Retrieve the module directory from cache
        val moduleDir = ModuleConfigUtil.getModuleDirectoryFromCache(project, locatorInfo.moduleId)
        if (moduleDir == null) {
            logger.warn("Module directory not found for module ID: ${locatorInfo.moduleId}")
            return null
        }

        // Step 3: Attempt to find the file using type-specific path
        val typeSpecificResult = locateFileByType(locatorInfo, moduleDir)
        if (typeSpecificResult != null) {
            logger.info("File found using type-specific path for locator: ${locatorInfo.id}")
            return typeSpecificResult
        }

        // Step 4: Fall back to searching the entire Maven source root
        logger.info("File not found using type-specific path for locator: ${locatorInfo.id}. Falling back to Maven source root search.")
        return locateFileInMavenSourceRoot(locatorInfo, moduleDir)
    }

    /**
     * Attempts to locate the file using the search key from the LocatorIndexManager.
     *
     * @param locatorInfo The locator info containing id, type, and module information.
     * @return The LocatorResult if found, null otherwise.
     */
    private fun locateFileBySearchKey(locatorInfo: LocatorBuilder.LocatorInfo): LocatorResult? {
        val searchKey = locatorInfo.searchKey
        val filePath = LocatorIndexManager.getInstance(project).getFilePath(searchKey)

        if (filePath == null) {
            logger.debug("No file path found for search key: $searchKey")
            return null
        }

        val virtualFile = VirtualFileManager.getInstance().findFileByUrl("file://$filePath")
        if (virtualFile == null) {
            logger.debug("No virtual file found at path: $filePath")
            return null
        }

        return runReadAction {
            val psiFile = PsiManager.getInstance(project).findFile(virtualFile)
            if (psiFile == null || !psiFile.isValid) {
                logger.debug("No PsiFile found for virtual file: $virtualFile")
                return@runReadAction null
            }

            val result = matches(psiFile, locatorInfo)
            if (result != null) {
                logger.info("File found using search key for locator: ${locatorInfo.id}")
            }
            return@runReadAction result
        }
    }

    /**
     * Attempts to locate the file using the type-specific path from typeToPathMapping.
     * This function performs the following steps:
     * 1. Retrieves the path information for the given type from typeToPathMapping.
     * 2. Navigates through the module directory structure to find the correct subdirectory.
     * 3. Locates the Maven source root.
     * 4. Constructs the full file path based on project and module names.
     * 5. Attempts to find the file and search within it.
     *
     * @param locatorInfo The locator info containing file type and other details.
     * @param moduleDir The module directory to start the search from.
     * @return The LocatorResult if the file is found, null otherwise.
     */
    private fun locateFileByType(
        locatorInfo: LocatorBuilder.LocatorInfo,
        moduleDir: VirtualFile,
    ): LocatorResult? {
        // Convert the type to lowercase for case-insensitive matching
        val type = locatorInfo.type.lowercase()

        // Split the path info into subModulePath and filePath
        val pathInfo = typeToPathMapping[type]?.split(TYPE_TO_PATH_SEPARATOR) ?: return null
        if (pathInfo.size != 2) {
            logger.warn("Invalid path info for type: $type")
            return null
        }
        val (subModulePath, filePath) = pathInfo

        // Traverse through subModules to find the current directory
        val currentDir = subModulePath.split(PACKAGE_PATH_SPLITTER).fold(moduleDir) { dir, subModule ->
            dir.findChild(subModule) ?: return null
        }

        // Find the Maven source root
        val mavenSourceRoot = findMavenSourceRoot(currentDir) ?: return null

        // Construct the package path
        val projectName = project.name.replace('-', '_')
        val moduleName = moduleDir.name
        val packagePath = FileUtil.toSystemIndependentName(
            "com/$projectName/$moduleName/${
                filePath.replace(
                    PACKAGE_PATH_SPLITTER.toString(),
                    DIR_PATH_SPLITTER.toString()
                )
            }"
        )

        // Construct the full file path
        val fullPath = FileUtil.toSystemIndependentName("${mavenSourceRoot.path}/$packagePath")

        // Attempt to find the file and search within it, wrapped in read action
        return runReadAction {
            try {
                VfsUtil.findFile(Paths.get(fullPath), true)?.let { searchForFile(it, locatorInfo) }
            } catch (e: Throwable) {
                logger.warn("Error locating file at path: $fullPath", e)
                null
            }
        }
    }

    /**
     * Searches for the file in all Maven source roots within the module directory and its submodules.
     * This method is called as a fallback when the type-specific search fails.
     *
     * The search process involves:
     * 1. Starting from the given module directory
     * 2. Performing a breadth-first search through all subdirectories
     * 3. Checking each directory to see if it's a Maven source root
     * 4. If a Maven source root is found, searching for the file within it
     * 5. If the file is not found, continuing the search in other directories
     *
     * @param locatorInfo The locator info containing file details such as id, type, and module information.
     * @param moduleDir The module directory to start the search from.
     * @return The LocatorResult if the file is found, null otherwise.
     */
    private fun locateFileInMavenSourceRoot(
        locatorInfo: LocatorBuilder.LocatorInfo,
        moduleDir: VirtualFile,
    ): LocatorResult? {
        // Initialize a queue for breadth-first search of directories
        val directoriesToSearch: Queue<VirtualFile> = LinkedList<VirtualFile>().apply { add(moduleDir) }

        while (directoriesToSearch.isNotEmpty()) {
            val currentDir = directoriesToSearch.poll()

            // Check if the current directory is a Maven source root
            findMavenSourceRoot(currentDir)?.let { mavenSourceRoot ->
                // If it is, search for the file within this Maven source root
                searchForFile(mavenSourceRoot, locatorInfo)?.let { result ->
                    logger.info("File found for locator ${locatorInfo.id} in Maven source root: ${mavenSourceRoot.path}")
                    return result
                }
            }

            // If file not found in current directory, add all subdirectories to the queue
            directoriesToSearch.addAll(currentDir.children.filter { it.isDirectory })
        }

        // If the file is not found in any Maven source root, log a warning and return null
        logger.warn("File not found for locator ${locatorInfo.id} in any Maven source root")
        return null
    }

    /**
     * Searches for the file in the given directory and its subdirectories.
     * This method performs a deep search based on the locator information.
     *
     * The search process involves:
     * 1. Setting up the search environment (PsiManager, FileType, and search scope)
     * 2. Using FilenameIndex to find all files with the specified extension
     * 3. Converting found files to PsiFiles
     * 4. Checking each file for a match against the locator information
     * 5. Returning the first matching result, if any
     *
     * @param searchDir The directory to start the search from.
     * @param locatorInfo The locator information containing file details.
     * @return The LocatorResult if a matching file is found, null otherwise.
     */
    private fun searchForFile(searchDir: VirtualFile, locatorInfo: LocatorBuilder.LocatorInfo): LocatorResult? {
        // Check if the search directory is valid
        if (!searchDir.isValid) {
            logger.warn("Search directory is not valid: ${searchDir.path}")
            return null
        }

        // Get the PsiManager instance for the current project
        val psiManager = PsiManager.getInstance(project)

        // Determine the file type based on the locator file extension
        val fileType = FileTypeManager.getInstance().getFileTypeByExtension(locatorFileExtension)

        // Create a search scope limited to the given directory and its subdirectories
        val directoryScope = GlobalSearchScopes.directoryScope(project, searchDir, true)

        // Further restrict the search scope to files of the determined file type
        val scope = GlobalSearchScope.getScopeRestrictedByFileTypes(directoryScope, fileType)

        // Perform the search within a read action
        return runReadAction {
            // Get all files with the specified extension
            val files = FilenameIndex.getAllFilesByExt(project, locatorFileExtension, scope)

            // Perform the conversion to PsiFiles and matching
            files.asSequence()
                .mapNotNull { file ->
                    // Check if the VirtualFile is valid before converting to PsiFile
                    val psiFile = psiManager.findFile(file)
                    if (psiFile == null || !psiFile.isValid) {
                        logger.debug("No valid PsiFile found for virtual file: $file")
                        return@mapNotNull null
                    }
                    matches(psiFile, locatorInfo)
                }
                .firstOrNull()
        }
    }


    /**
     * Finds the Maven source root directory within the given module directory.
     * The Maven source root is typically located at 'src/main/java' within a module.
     *
     * @param moduleDir The VirtualFile representing the module directory to search in.
     * @return The VirtualFile representing the Maven source root if found, null otherwise.
     */
    private fun findMavenSourceRoot(moduleDir: VirtualFile): VirtualFile? {
        // Navigate through the expected Maven directory structure
        val mavenSourceRoot = moduleDir.findChild("src")?.findChild("main")?.findChild("java")

        if (mavenSourceRoot == null) {
            // Log info if the Maven source root is not found
            logger.info("Maven source root not found in: ${moduleDir.path}")
        } else {
            // Log info message if Maven source root is found
            logger.info("Maven source root found: ${mavenSourceRoot.path}")
        }

        return mavenSourceRoot
    }

    /**
     * Locates the file and navigates to the target based on the provided locator info.
     * This method combines file location and navigation, handling any errors that occur during the process.
     * It runs as a background task with progress indication.
     *
     * @param info The LocatorInfo containing necessary information for locating and navigating.
     * @throws IllegalArgumentException if the provided locator info is invalid.
     * @throws LocatorException if there's an error during the location or navigation process.
     */
    fun locateFileAndNavigateToTarget(info: LocatorBuilder.LocatorInfo) {
        // Validate the locator info before proceeding
        if (!validateInfo(info, project)) {
            logger.warn("Invalid locator info: $info")
            throw IllegalArgumentException("Invalid locator info")
        }

        // Run the file location and navigation as a background task
        ProgressManager.getInstance().run(object : Task.Backgroundable(project, "Locating File", true) {
            override fun run(indicator: ProgressIndicator) {
                try {
                    // Set up progress indicator
                    indicator.isIndeterminate = false
                    indicator.fraction = 0.0
                    indicator.text = "Searching for file..."

                    // Locate the file using read action
                    val locatorResult = runReadAction {
                        locateFile(info)
                    }

                    // Check if file was found
                    if (locatorResult == null) {
                        ApplicationManager.getApplication().invokeLater {
                            throw IllegalArgumentException("No matching file found")
                        }
                        return
                    }

                    // Update progress for file opening
                    indicator.fraction = 0.5
                    indicator.text = "Opening file..."

                    // Open and navigate to the target file on the EDT
                    ApplicationManager.getApplication().invokeLater({
                        try {
                            if (!openAndNavigateToTarget(locatorResult)) {
                                throw LocatorException("Failed to open and navigate to target file")
                            }
                        } catch (e: Exception) {
                            logger.warn("Error navigating to target file", e)
                            throw LocatorException("Failed to open and navigate to target file")
                        }
                    }, ModalityState.NON_MODAL)

                    // Mark operation as completed
                    indicator.fraction = 1.0
                    indicator.text = "Operation completed"
                } catch (e: Exception) {
                    // Log error and throw exception on EDT
                    logger.warn("Error locating file and navigating to target", e)
                    ApplicationManager.getApplication().invokeLater({
                        throw LocatorException("Failed to open and navigate to target file")
                    }, ModalityState.NON_MODAL)
                }
            }

            // Handle task cancellation
            override fun onCancel() {
                logger.info("File location operation was cancelled")
            }
        })
    }

    /**
     * Validates the LocatorInfo instance to ensure all necessary fields are non-empty.
     *
     * @param info The LocatorInfo instance to validate.
     * @param project The current IntelliJ IDEA project.
     * @return Boolean indicating whether the validation passed.
     */
    fun validateInfo(info: LocatorBuilder.LocatorInfo, project: Project): Boolean {
        // Check if any of the essential fields in LocatorInfo are empty
        return !(info.id.isEmpty() || info.type.isEmpty() || info.moduleId.isEmpty())
    }

    /**
     * Retrieves the project ID from ProjectPluginSettings.
     *
     * @param project Current project context.
     * @return The ID of the project as a String, or null if not available.
     */
    fun getProjectId(project: Project): String? {
        ProjectPluginSettings.getInstance(project).ensureInitialized(project)
        return ProjectPluginSettings.getInstance(project).projectId
    }

    /**
     * Retrieves the module ID for the module containing the given PsiElement.
     *
     * @param element PsiElement to get the module for.
     * @return The ID of the module as a String, or null if the module is not found or ID cannot be determined.
     */
    fun getModuleId(element: PsiElement): String? {
        return getModuleIdFromModule(element)
    }

    /**
     * Retrieves the module ID from the module's configuration file.
     *
     * @param element The PsiElement associated with the module, used to locate the configuration file.
     * @return The ID of the module as a String, or null if not found or if the element is not in a Java file.
     */
    fun getModuleIdFromModule(element: PsiElement): String? {
        // Check if the element is valid before processing
        if (!element.isValid) {
            logger.warn(getI18nString("invalid.psi.element", element::class.java.simpleName))
            return null
        }

        // Extract the containing file outside of the read action
        val psiFile = element.containingFile
        if (psiFile !is PsiJavaFile) {
            return null
        }

        // Perform the property search within a read action
        return runReadAction {
            // Attempts to find the "id" property in the "module" section of the nearest configuration file
            ModuleConfigUtil.findNearestConfigProperty(psiFile, "module", "id")
        }
    }

    /**
     * Retrieves the module name from the module's configuration file.
     *
     * @param element The PsiElement associated with the module, used to locate the configuration file.
     * @return The name of the module as a String, or null if not found or if the element is not in a Java file.
     */
    fun getModuleNameFromModule(element: PsiElement): String? {
        // Check if the element is valid before processing
        if (!element.isValid) {
            logger.warn(getI18nString("invalid.psi.element", element::class.java.simpleName))
            return null
        }

        // Extract the containing file outside of the read action
        val psiFile = element.containingFile
        if (psiFile !is PsiJavaFile) {
            return null
        }

        // Perform the property search within a read action
        return runReadAction {
            // Attempts to find the "name" property in the "module" section of the nearest configuration file
            ModuleConfigUtil.findNearestConfigProperty(psiFile, "module", "name")
        }
    }
}
package com.think1024.tocodesign.ideaplugin.services.locator

import com.google.gson.JsonObject
import com.intellij.find.FindManager
import com.intellij.find.FindModel
import com.intellij.find.findInProject.FindInProjectManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiElement
import com.think1024.tocodesign.ideaplugin.services.locator.LocatorBuilder.LocatorInfo
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.toco.TocoWebViewLauncher
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import com.think1024.tocodesign.ideaplugin.utils.getStringOrNull
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.OpenFileDescriptor
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.PsiManager
import com.intellij.psi.search.FileTypeIndex
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.psi.search.PsiSearchHelper
import com.intellij.psi.search.PsiSearchScopeUtil
import com.intellij.util.indexing.FileBasedIndex
import java.net.URL
import java.util.*


/**
 * LocatorService is responsible for managing locator-related operations in the IntelliJ IDEA plugin.
 * It handles communication with the Socket.IO server, processes locator requests and responses,
 * and coordinates the opening of specific elements in the desktop application.
 *
 * This service is scoped at the PROJECT level, meaning each open project in IDEA will have its own instance.
 *
 * @property project The IntelliJ IDEA project associated with this service instance.
 */
@Service(Service.Level.PROJECT)
class LocatorService(private val project: Project) {
    // Logger for this class
    private val logger = Logger.getInstance(LocatorService::class.java)

    private val highlightLocatorService = project.getService(HighlightLocatorService::class.java)

    // Locator rulers for different element types
    private val classLocatorRuler = ClassLocatorRuler(project)
    private val methodLocatorRuler = MethodLocatorRuler(project)

    // Builder for creating locator information
    private val locatorBuilder = LocatorBuilder(project)

    private fun convertUrl(option: LocatorInfo): String {
        val type = option.type.lowercase()
        var host = ApplicationPluginSettings.getInstance().frontendHost
        return "${host}/${option.projectId}/system-design?uuid=${option.id}&itemType=${option.type.lowercase()}&moduleId=${option.moduleId}"
//        return when (type) {
//            "vo" -> "$host/${option.projectId}/${option.moduleId}/model/vo?uuid=${option.id}"
//            "bo" -> "$host/${option.projectId}/${option.moduleId}/model/er?bo=${option.id}"
//            "bto" -> "$host/${option.projectId}/${option.moduleId}/service/write?uuid=${option.id}"
//            "dto" -> "$host/${option.projectId}/${option.moduleId}/model/dto?uuid=${option.id}"
//            "entity" -> "$host/${option.projectId}/${option.moduleId}/model/er?uuid=${option.id}"
//            "rpc" -> "$host/${option.projectId}/${option.moduleId}/rpc?uuid=${option.id}"
//            "api" -> "$host/${option.projectId}/${option.moduleId}/api?uuid=${option.id}"
//            "qto" -> "$host/${option.projectId}/${option.moduleId}/service/read?uuid=${option.id}"
//            "eo" -> "$host/${option.projectId}/${option.moduleId}/model/eo?uuid=${option.id}"
//            "enum" -> "$host/${option.projectId}/${option.moduleId}/model/enum?uuid=${option.id}"
//            "mo" -> "$host/${option.projectId}/${option.moduleId}/service/msg?tab=normal&&uuid=${option.id}"
//            "dmo" -> "$host/${option.projectId}/${option.moduleId}/service/msg?tab=domain&&uuid=${option.id}"
//            "flow_node" -> "$host/${option.projectId}/${option.moduleId}/service/flow?isNode=true&uuid=${option.id}"
//            "flow" -> "$host/${option.projectId}/${option.moduleId}/service/flow?uuid=${option.id}"
//            "flow_list" -> "$host/${option.projectId}/${option.moduleId}/service/flow"
//            else -> ""
//        }
    }

    /**
     * Opens the specified PsiElement in the toco editor
     * This method constructs a locator request for the given element and sends it via the Socket.IO connection.
     *
     * @param element The PSI element to be opened in the toco editor.
     */
    fun openTocoWebViewEditor(element: PsiElement) {
        val payload = locatorBuilder.buildFromPsiElement(element)
        payload?.let {
            val name = if (payload.type == "rpc" || payload.type == "api") {
                payload.name
            } else {
                payload.name.let {
                    it.split("(?=[A-Z])".toRegex()).filter { it.isNotEmpty() }
                        .joinToString("_") { s -> s.lowercase() }
                }
            }
            val url = convertUrl(payload)
            val type = URL(url).path.split("/").last()
            TocoWebViewLauncher.openPage(project, url, "[${payload.moduleName}] $name", type)
        }
    }

    /**
     * Handles incoming socket messages related to locator actions.
     * Based on the action specified in the message, this function delegates
     * the processing to the appropriate handler method.
     *
     * @param message The socket request message containing the action and any relevant data.
     *
     * The method processes actions as follows:
     * - If the action is LOCATE, it calls `handleLocateMessage` to process file location requests.
     * - If the action is HIGHLIGHT, it delegates to `highlightLocatorService.handleHighlightMessage`
     *   to manage highlight requests.
     * - For any other action, it sends a system error response back to the client,
     *   indicating an invalid message body.
     *
     * This function ensures that only recognized actions are processed,
     * maintaining robustness against unexpected input.
     */
    fun handleLocatorMessage(message: JsonObject, action: String, response: (result: String?) -> Unit) {
        when (action) {
            "locate" -> handleLocateMessage(message, response)
            "highlight" -> highlightLocatorService.handleHighlightMessage(message, response)
            else -> { response(getI18nString("locator.invalid.message.body")) }
        }
    }

    /**
     * 导航到文件的指定位置
     */
    private fun navigateToFilePosition(project: Project, file: VirtualFile, offset: Int, searchText: String) {
        // 确保在EDT线程中执行
        ApplicationManager.getApplication().invokeLater {
            // 获取PsiFile
            val psiFile = PsiManager.getInstance(project).findFile(file) ?: return@invokeLater

            // 打开文件
            val descriptor = OpenFileDescriptor(project, file, offset)
            val editor = FileEditorManager.getInstance(project).openTextEditor(descriptor, true) ?: return@invokeLater

            // 移动光标并滚动到位置
            editor.caretModel.moveToOffset(offset)
            editor.scrollingModel.scrollToCaret(ScrollType.CENTER)

            // 高亮显示匹配文本
            val document = PsiDocumentManager.getInstance(project).getDocument(psiFile) ?: return@invokeLater
            val endOffset = offset + searchText.length
            if (endOffset <= document.textLength) {
                editor.selectionModel.setSelection(offset, endOffset)
            }
        }
    }
    /**
     * Handles incoming locator messages from the SocketClient.
     *
     * This method processes a locator request message, validates the locator information,
     * determines the appropriate locator ruler based on the type, and attempts to locate
     * and navigate to the target file. If any error occurs during this process, an appropriate
     * error response is sent back.
     *
     * @param message The SocketRequestMessage containing the locator information.
     */
    private fun handleLocateMessage(message: JsonObject?, response: (result: String?) -> Unit) {
        // Check if the extracted locator information is null
        if (message == null) {
            // body无效，回调前端显示错误
            logger.warn("Invalid message body")

            // Send an error response if the locator information is missing
            response(getI18nString("locator.invalid.message.body"))
            return
        }

        // Convert the map to a LocatorInfo object for further processing
        val locatorInfo = LocatorBuilder.LocatorInfo.fromMap(message)
        val searchKey = locatorInfo.searchKey
        // 请补充逻辑
        // 在UI线程中执行搜索操作

        // 在UI线程中执行搜索
        ApplicationManager.getApplication().invokeLater {
            try {
                // 配置查找模型
                val findModel = FindModel().apply {
                    stringToFind = searchKey
                    isCaseSensitive = false
                    isWholeWordsOnly = false
                    isRegularExpressions = false
                }

                val findManager = FindManager.getInstance(project)
                val searchScope = GlobalSearchScope.projectScope(project)

                // 获取项目中所有的文件
                val allFiles = mutableListOf<VirtualFile>()
                FileBasedIndex.getInstance().processAllKeys(
                    FileTypeIndex.NAME,
                    { fileType ->
                        FileBasedIndex.getInstance().getContainingFiles(
                            FileTypeIndex.NAME,
                            fileType,
                            searchScope
                        ).forEach { allFiles.add(it) }
                        true
                    },
                    searchScope,
                    null
                )

                var foundFile: VirtualFile? = null
                var foundOffset = -1

                // 遍历文件查找匹配内容
                for (file in allFiles) {
                    // 获取PsiFile
                    val psiFile = PsiManager.getInstance(project).findFile(file) ?: continue

                    // 检查PsiFile是否在搜索范围内
                    if (!PsiSearchScopeUtil.isInScope(searchScope, psiFile)) continue

                    // 获取文件文档内容（用于搜索）
                    val document = PsiDocumentManager.getInstance(project).getDocument(psiFile) ?: continue
                    val fileContent = document.text

                    // 在文件内容中查找匹配（使用正确的参数类型）
                    val findResult = findManager.findString(fileContent, 0, findModel)
                    if (findResult.isStringFound) {
                        foundFile = file
                        foundOffset = findResult.startOffset
                        break // 找到第一个匹配后停止
                    }
                }

                if (foundFile != null && foundOffset != -1) {
                    navigateToFilePosition(project, foundFile, foundOffset, searchKey)
                    response(getI18nString("locator.navigation.success", searchKey))
                } else {
                    response(getI18nString("locator.no.matches.found", searchKey))
                }
            } catch (e: Exception) {
                logger.error("Error during file search", e)
                response(getI18nString("locator.search.error", e.localizedMessage))
            }
        }
    }

    companion object {
        /**
         * Retrieves the LocatorService instance for the specified project.
         * This method provides a convenient way to access the LocatorService for a given project.
         *
         * @param project The project for which to obtain the service instance.
         * @return The LocatorService instance associated with the given project.
         */
        fun getInstance(project: Project): LocatorService = project.getService(LocatorService::class.java)
    }
}

package com.think1024.tocodesign.ideaplugin.toco

import TocoFileType
import com.intellij.openapi.fileTypes.FileType
import com.intellij.openapi.fileTypes.FileTypeManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.VirtualFileSystem
import com.intellij.testFramework.LightVirtualFile
import com.intellij.ui.AnimatedIcon
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.nio.file.Paths
import javax.swing.Icon
import kotlin.io.path.extension

class IgnoredVirtualFile(
    private val file: MergeFile,
    private val project: Project?
) : LightVirtualFile(
    Paths.get(file.mergedPath).fileName.toString(),
    FileTypeManager.getInstance().getFileTypeByExtension(Paths.get(file.mergedPath).extension),
    file.mergedContent
) {
    fun getMergeFile(): MergeFile {
        return this.file
    }

    override fun getPath(): String {
        return Paths.get(project?.basePath ?: "", file.mergedPath).toString()
    }

    override fun getOutputStream(requestor: Any?, newModificationStamp: Long, newTimeStamp: Long): OutputStream {
        val file = File(this.path)
        file.parentFile?.mkdirs()
        return FileOutputStream(file)
    }

    override fun getParent(): VirtualFile? {
        return LightVirtualFile(Paths.get(file.mergedPath).parent.toString())
    }

    override fun isDirectory(): Boolean {
        return false
    }
}
package com.think1024.tocodesign.ideaplugin.actions

import com.intellij.notification.NotificationType
import com.intellij.openapi.actionSystem.ActionGroup
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.services.NotificationService
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.toco.Fossil
import com.think1024.tocodesign.ideaplugin.toco.Item
import com.think1024.tocodesign.ideaplugin.utils.ProjectConfigUtil

fun getInfoFromActionEvent(e: AnActionEvent): Pair<Item, Project>? {
    val project = e.getData(CommonDataKeys.PROJECT) ?: return null
    if (project.basePath == null) {
        return null
    }
    val info = ProjectConfigUtil.getProjectInfo(project.basePath!!) ?: return null
    val name = info["name"] ?: return null
    val id = info["id"] ?: return null
    return Pair(Item(id, name), project)
}

// Action to open Fossil UI
class OpenFossilUIAction : AnAction("Open Fossil UI") {
    override fun actionPerformed(e: AnActionEvent) {
        val (item, project) = getInfoFromActionEvent(e) ?: Pair(null, null)
        if (item == null || project?.basePath == null) {
            return
        }
        ApplicationManager.getApplication().executeOnPooledThread {
            try {
                val result = Fossil.openUI(item, project.basePath!!)
                if (!result.success) {
                    NotificationService.instance.notify(
                        "Failed to open Fossil UI",
                        result.message ?: "",
                        project,
                        NotificationType.ERROR
                    )
                }
            } catch (e: Exception) {
                NotificationService.instance.notify(
                    "Failed to open Fossil UI",
                    e.message ?: "",
                    project,
                    NotificationType.ERROR
                )
            }
        }
    }
}

// Action to clean Fossil files
class CleanFossilAction : AnAction("Clean Fossil Files") {
    override fun actionPerformed(e: AnActionEvent) {
        val (item, project) = getInfoFromActionEvent(e) ?: Pair(null, null)
        if (item == null || project?.basePath == null) {
            return
        }
        try {
            val result = Fossil.cleanFossil(item, project.basePath!!)
            if (result) {
                NotificationService.instance.notify(
                    "Clean Fossil files successfully",
                    "",
                    project,
                    NotificationType.INFORMATION
                )
            } else {
                NotificationService.instance.notify(
                    "Failed to clean Fossil files",
                    "",
                    project,
                    NotificationType.ERROR
                )
            }
        } catch (e: Exception) {
            NotificationService.instance.notify(
                "Failed to clean Fossil files",
                e.message ?: "",
                project,
                NotificationType.ERROR
            )
        }
    }
}

class TocoActionGroup : ActionGroup("Toco", true) {
    override fun update(e: AnActionEvent) {
        if (e.project == null) {
            e.presentation.isEnabledAndVisible = false
        } else {
            e.presentation.isEnabledAndVisible = ProjectPluginSettings.getInstance(e.project!!).projectId != null
        }
    }

    override fun getChildren(e: AnActionEvent?): Array<AnAction> {
        return arrayOf(
            OpenFossilUIAction(),
            CleanFossilAction(),
        )
    }
}
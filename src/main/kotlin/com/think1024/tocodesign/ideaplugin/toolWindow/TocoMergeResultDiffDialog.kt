@file:Suppress("UnstableApiUsage")

package com.think1024.tocodesign.ideaplugin.toolWindow

import com.intellij.CommonBundle
import com.intellij.configurationStore.StoreReloadManager
import com.intellij.diff.DiffContentFactory
import com.intellij.diff.DiffDialogHints
import com.intellij.diff.DiffManager
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.openapi.application.ModalityState
import com.intellij.openapi.application.runInEdt
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileTypes.FileTypeManager
import com.intellij.openapi.progress.util.BackgroundTaskUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.openapi.vcs.VcsBundle
import com.intellij.openapi.vcs.VcsConfiguration
import com.intellij.openapi.vcs.changes.ui.*
import com.intellij.openapi.vcs.merge.MergeConflictsTreeTable
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.wm.IdeFocusManager
import com.intellij.testFramework.LightVirtualFile
import com.intellij.ui.DoubleClickListener
import com.intellij.ui.TableSpeedSearch
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.treeStructure.treetable.ListTreeTableModelOnColumns
import com.intellij.ui.treeStructure.treetable.TreeTable
import com.intellij.ui.treeStructure.treetable.TreeTableModel
import com.intellij.util.EditSourceOnDoubleClickHandler
import com.intellij.util.containers.ContainerUtil
import com.intellij.util.containers.Convertor
import com.intellij.util.ui.ColumnInfo
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import com.intellij.util.ui.tree.TreeUtil
import com.think1024.tocodesign.ideaplugin.utils.FileUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import org.jetbrains.annotations.NonNls
import java.awt.BorderLayout
import java.awt.GridLayout
import java.awt.event.ActionEvent
import java.awt.event.MouseEvent
import java.nio.file.Paths
import javax.swing.*
import javax.swing.table.AbstractTableModel
import javax.swing.tree.DefaultMutableTreeNode
import javax.swing.tree.DefaultTreeModel
import javax.swing.tree.TreeNode
import kotlin.io.path.extension


data class TocoDiffFile(
    val path: String,
    val oursContent: String,
    val theirsContent: String,
    val mergedContent: String,
)

fun TocoDiffFile.getVirtualFile(project: Project?): VirtualFile {
    return FileUtil.getVirtualFile(this.path, project) ?: LightVirtualFile(this.path, null, this.mergedContent)
}


fun TreeModelBuilder.insertMergedFilesIntoNode(files: Collection<TocoDiffFile>, subtreeRoot: ChangesBrowserNode<*>) {
    for (file in files) {
        val virtualFile = file.getVirtualFile(myProject)
        insertChangeNode(virtualFile, subtreeRoot, ChangesBrowserNode.createFile(myProject, virtualFile))
    }
}

fun TreeModelBuilder.setMergedFiles(files: Collection<TocoDiffFile>, root: ChangesBrowserNode<*>?): TreeModelBuilder {
    if (ContainerUtil.isEmpty(files)) return this
    insertMergedFilesIntoNode(files, root ?: createTagNode(null))
    return this
}

fun buildFromMergedFiles(
    project: Project,
    grouping: ChangesGroupingPolicyFactory,
    mergedFiles: Collection<TocoDiffFile>,
): DefaultTreeModel {
    val builder = TreeModelBuilder(project, grouping)
    builder.setMergedFiles(mergedFiles, null)
    return builder.build()
}

open class TocoMergeResultDiffDialog(
    private val project: Project,
    files: List<TocoDiffFile>?,
) : DialogWrapper(project) {
    private var allFiles = files?.toMutableList() ?: mutableListOf()
    private lateinit var table: TreeTable
    private var detailButton: JButton? = null
    private val tableModel = ListTreeTableModelOnColumns(DefaultMutableTreeNode(), createColumns())

    private val descriptionLabel = JLabel(getI18nString("code.generate.resolve.loading"))

    private var groupByDirectory: Boolean
        get() {
            return  VcsConfiguration.getInstance(project).GROUP_MULTIFILE_MERGE_BY_DIRECTORY
        }
        set(value) {
            VcsConfiguration.getInstance(project).GROUP_MULTIFILE_MERGE_BY_DIRECTORY = value
        }

    private val mergeFileRenderer = object : ChangesBrowserNodeRenderer(project, { !groupByDirectory }, false) {
        override fun calcFocusedState() = UIUtil.isAncestor(<EMAIL>,
            IdeFocusManager.getInstance(project).focusOwner)
    }

    init {
        StoreReloadManager.getInstance(project).blockReloadingProjectOnExternalChanges()
        title = getI18nString("code.generate.resolve.merge.diff")

        mergeFileRenderer.font = UIUtil.getListFont()

        @Suppress("LeakingThis")
        init()

        updateTree()
        table.tree.selectionModel.addTreeSelectionListener { updateButtonState() }
        updateButtonState()
        TreeUtil.promiseSelectFirstLeaf(table.tree)
        object : DoubleClickListener() {
            override fun onDoubleClick(event: MouseEvent): Boolean {
                if (EditSourceOnDoubleClickHandler.isToggleEvent(table.tree, event)) return false

                val closestPath = table.tree.getClosestPathForLocation(event.x, event.y)
                if (closestPath != null) {
                    val selectedNode = closestPath.lastPathComponent
                    val nodeData = (selectedNode as? DefaultMutableTreeNode)?.userObject
                    if (nodeData != null) {
                        showDiffDialog()
                    }
                }
                return true
            }
        }.installOn(table.tree)

        TableSpeedSearch.installOn(table, Convertor { (it as? VirtualFile)?.name })

        val modalityState = ModalityState.stateForComponent(descriptionLabel)
        BackgroundTaskUtil.executeOnPooledThread(disposable) {
            runInEdt(modalityState) {
                descriptionLabel.text = getI18nString("code.generate.resolve.merge.diff.confirm", allFiles.size.toString())
            }
        }
    }

    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.add(descriptionLabel, BorderLayout.NORTH)
        val mainPanel = JPanel(BorderLayout())
        
        table = MergeConflictsTreeTable(tableModel).apply {
            setTreeCellRenderer(mergeFileRenderer)
            rowHeight = mergeFileRenderer.preferredSize.height
            preferredScrollableViewportSize = JBUI.size(600, 300)
        }
        mainPanel.add(JBScrollPane(table), BorderLayout.CENTER)
        
        val buttonPanel = JPanel(GridLayout(4, 1, 0, 5))

        if (allFiles.isNotEmpty()) {
            val detailAction = object : AbstractAction() {
                override fun actionPerformed(e: ActionEvent) {
                    showDiffDialog()
                }
            }
            detailAction.putValue(DEFAULT_ACTION, java.lang.Boolean.TRUE)
            detailButton = createJButtonForAction(detailAction).apply {
                text = getI18nString("code.generate.resolve.merge.diff.detail")
            }
            buttonPanel.add(detailButton)
        }

        val buttonWrapper = JPanel(BorderLayout())
        buttonWrapper.add(buttonPanel, BorderLayout.NORTH)
        mainPanel.add(buttonWrapper, BorderLayout.EAST)
        
        panel.add(mainPanel, BorderLayout.CENTER)
        
        val groupByDirectoryCheckBox = JCheckBox(VcsBundle.message("multiple.file.merge.group.by.directory.checkbox"), groupByDirectory)
        groupByDirectoryCheckBox.addActionListener { toggleGroupByDirectory(groupByDirectoryCheckBox.isSelected) }
        panel.add(groupByDirectoryCheckBox, BorderLayout.SOUTH)
        
        return panel
    }

    private fun createColumns(): Array<ColumnInfo<*, *>> {
        val columns = ArrayList<ColumnInfo<*, *>>()
        columns.add(object : ColumnInfo<DefaultMutableTreeNode, Any>(VcsBundle.message("multiple.file.merge.column.name")) {
            override fun valueOf(node: DefaultMutableTreeNode) = node.userObject
            override fun getColumnClass(): Class<*> = TreeTableModel::class.java
        })

        return columns.toTypedArray()
    }

    private fun toggleGroupByDirectory(state: Boolean) {
        groupByDirectory = state
        val selectedFiles = getSelectedFiles()
        var firstSelectedFile = selectedFiles.firstOrNull()
        updateTree()
        if (firstSelectedFile != null) {
            val node = TreeUtil.findNodeWithObject(tableModel.root as DefaultMutableTreeNode, firstSelectedFile)
            node?.let { TreeUtil.selectNode(table.tree, node) }
        }
    }

    private fun updateTree() {
        val factory = when {
            groupByDirectory -> ChangesGroupingSupport.getFactory(ChangesGroupingSupport.DIRECTORY_GROUPING)
            else -> NoneChangesGroupingFactory
        }
        val model = buildFromMergedFiles(project, factory, allFiles)
        tableModel.setRoot(model.root as TreeNode)
        TreeUtil.expandAll(table.tree)
        (table.model as? AbstractTableModel)?.fireTableDataChanged()
    }

    private fun updateButtonState() {
        val selectedFiles = getSelectedFiles()
        val haveSelect = selectedFiles.any()
        detailButton?.isEnabled = haveSelect
    }

    private fun getSelectedFiles(): List<TocoDiffFile> {
        val virtualFiles = VcsTreeModelData.selected(table.tree).userObjects(VirtualFile::class.java)
        val files = virtualFiles.mapNotNull { virtualFile ->
            allFiles.find { mergeFile -> virtualFile.path.contains(mergeFile.path) }
        }
        return files
    }

    override fun createActions(): Array<Action> {
        cancelAction.putValue(Action.NAME, CommonBundle.getCloseButtonText())
        return arrayOf(cancelAction)
    }

    override fun dispose() {
        StoreReloadManager.getInstance(project).unblockReloadingProjectOnExternalChanges()
        super.dispose()
    }

    @NonNls
    override fun getDimensionServiceKey(): String = "TocoMergeResultDiffDialog"

    private fun updateModelFromFiles() {
        var selIndex = table.selectionModel.minSelectionIndex
        updateTree()
        if (selIndex >= table.rowCount) {
            selIndex = table.rowCount - 1
        }
        table.selectionModel.setSelectionInterval(selIndex, selIndex)
        table.requestFocusInWindow()
    }

    private fun handleSingleFile(file: TocoDiffFile) {
        val contentFactory: DiffContentFactory = DiffContentFactory.getInstance()
        val fileType = FileTypeManager.getInstance().getFileTypeByExtension(Paths.get(file.path).extension)
        val ours = contentFactory.create(file.oursContent, fileType)
        val merged = contentFactory.create(file.mergedContent, fileType)
        val theirs = contentFactory.create(file.theirsContent, fileType)

        val diffRequest = SimpleDiffRequest(
            getI18nString("code.generate.resolve.merge.diff"),
            ours,
            merged,
            theirs,
            "Local",
            "Merged",
            "Toco",
        )
        DiffManager.getInstance().showDiff(project, diffRequest, DiffDialogHints.MODAL)
    }

    private fun showDiffDialog() {
        val files = getSelectedFiles()
        if (files.isEmpty()) return
        for (file in files) {
            handleSingleFile(file)
        }
        updateModelFromFiles()
    }

    override fun getPreferredFocusedComponent(): JComponent? = table

    companion object {
        private val LOG = Logger.getInstance(TocoMultipleFileMergeDialog::class.java)
    }
}

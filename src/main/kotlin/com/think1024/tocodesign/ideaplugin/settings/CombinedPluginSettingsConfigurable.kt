package com.think1024.tocodesign.ideaplugin.settings

import com.intellij.ide.BrowserUtil
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.options.Configurable
import com.intellij.openapi.project.Project
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.components.JBTabbedPane
import com.intellij.util.ui.JBUI
import com.think1024.tocodesign.ideaplugin.utils.ConfigUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import java.awt.BorderLayout
import java.awt.Cursor
import java.awt.Dimension
import java.awt.Font
import java.awt.event.ComponentAdapter
import java.awt.event.ComponentEvent
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import javax.swing.JComponent
import javax.swing.JLabel
import javax.swing.JPanel
import javax.swing.JScrollPane

/**
 * A combined configurable that manages both application-level and project-level settings
 * for the plugin in a single settings page with two tabs.
 *
 * @param project The current project instance
 */
class CombinedPluginSettingsConfigurable(private val project: Project) : Configurable {

    companion object {
        private val LOG = Logger.getInstance(CombinedPluginSettingsConfigurable::class.java)
        const val APPLICATION_TAB_INDEX = 1
        const val INDEXING_TAB_INDEX = 2
    }

    /**
     * Instances of individual configurable components for application, project, and indexing settings.
     */
    private val applicationConfigurable = ApplicationPluginSettingsConfigurable(project)
    private val projectConfigurable = ProjectPluginSettingsConfigurable(project)
    private val indexingConfigurable = IndexingSettingsConfigurable(project)

    /**
     * The tabbed pane component used to switch between application, project, and indexing settings.
     * It's nullable and lazily initialized in the createComponent() method.
     */
    private var tabbedPane: JBTabbedPane? = null

    /**
     * The main panel that contains all UI components for the combined settings.
     * It's nullable and lazily initialized in the createComponent() method.
     */
    private var mainPanel: JPanel? = null

    /**
     * Returns the display name for this settings page.
     *
     * @return The name to be displayed in the Settings dialog.
     */
    override fun getDisplayName(): String = getI18nString("settings.title")

    /**
     * Creates and returns the main settings component.
     *
     * This method sets up a custom panel with a JBTabbedPane and a download link:
     * The JBTabbedPane contains three tabs: one for application-wide settings, another for project-specific settings,
     * and a third for indexing settings.
     * The download link for Toco Design App is placed on the top right of the panel.
     *
     * @return JComponent representing the main settings panel.
     */
    override fun createComponent(): JComponent {
        if (mainPanel == null) {
            LOG.debug("Creating combined settings component")
            mainPanel = JPanel(BorderLayout()).apply {
                // Set minimum size to ensure the panel doesn't get too small
                minimumSize = Dimension(400, 300)
                // Set preferred size for initial display
                preferredSize = Dimension(600, 400)
            }

            tabbedPane = JBTabbedPane().apply {
                // Make tabbed pane fill the available space
                minimumSize = Dimension(400, 300)
                preferredSize = Dimension(600, 400)
            }

            // Add project settings tab
            if (project.basePath != null) {
                LOG.debug("Adding project settings tab")
                val projectPanel = projectConfigurable.createComponent()
                val projectScrollPane = JBScrollPane(projectPanel).apply {
                    verticalScrollBarPolicy = JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED
                    horizontalScrollBarPolicy = JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED
                }
                tabbedPane?.addTab(getI18nString("settings.tab.project"), projectScrollPane)
            }

            // Add application settings tab
            LOG.debug("Adding application settings tab")
            val applicationPanel = applicationConfigurable.createComponent()
            val applicationScrollPane = JScrollPane(applicationPanel).apply {
                verticalScrollBarPolicy = JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED
                horizontalScrollBarPolicy = JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED
            }
            tabbedPane?.addTab(getI18nString("settings.tab.application"), applicationScrollPane)

            // Add indexing settings tab
//            if (project.basePath != null) {
//                LOG.debug("Adding indexing settings tab")
//                val indexingPanel = indexingConfigurable.createComponent()
//                val indexingScrollPane = JScrollPane(indexingPanel).apply {
//                    verticalScrollBarPolicy = JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED
//                    horizontalScrollBarPolicy = JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED
//                }
//                tabbedPane?.addTab(getI18nString("settings.tab.indexing"), indexingScrollPane)
//            }

            // Add components to the main panel with proper constraints
            mainPanel?.add(tabbedPane, BorderLayout.CENTER)

            // Add component listener to handle resize events
            mainPanel?.addComponentListener(object : ComponentAdapter() {
                override fun componentResized(e: ComponentEvent) {
                    // Ensure the tabbed pane fills the available space
                    tabbedPane?.size = mainPanel?.size ?: Dimension(600, 400)
                    mainPanel?.revalidate()
                    mainPanel?.repaint()
                }
            })

            LOG.debug("Combined settings component created successfully")
        }
        return mainPanel ?: JPanel()
    }

    fun selectApplicationTab() {
        if (tabbedPane == null) {
            createComponent()
        }
        tabbedPane?.selectedIndex = APPLICATION_TAB_INDEX
    }

    fun selectIndexingTab() {
        if (tabbedPane == null) {
            createComponent()
        }
        tabbedPane?.selectedIndex = INDEXING_TAB_INDEX
    }

    /**
     * Checks if either application, project, or indexing settings have been modified.
     *
     * @return true if any settings have been modified, false otherwise.
     */
    override fun isModified(): Boolean {
        val modified =
            applicationConfigurable.isModified || projectConfigurable.isModified || indexingConfigurable.isModified
        LOG.debug("Settings modified: $modified")
        return modified
    }

    /**
     * Applies changes from all application, project, and indexing settings.
     *
     * This method is called when the user clicks the Apply button in the Settings dialog.
     */
    override fun apply() {
        LOG.info("Applying settings changes")
        applicationConfigurable.apply()
        projectConfigurable.apply()
        indexingConfigurable.apply()
    }

    /**
     * Resets all application, project, and indexing settings to their saved state.
     *
     * This method is called when the user clicks the Reset button in the Settings dialog.
     */
    override fun reset() {
        LOG.info("Resetting settings")
        applicationConfigurable.reset()
        projectConfigurable.reset()
        indexingConfigurable.reset()
    }

    /**
     * Releases resources used by all application, project, and indexing settings.
     *
     * This method is called when the settings dialog is closed.
     */
    override fun disposeUIResources() {
        LOG.debug("Disposing UI resources")
        applicationConfigurable.disposeUIResources()
        projectConfigurable.disposeUIResources()
        indexingConfigurable.disposeUIResources()
    }
}

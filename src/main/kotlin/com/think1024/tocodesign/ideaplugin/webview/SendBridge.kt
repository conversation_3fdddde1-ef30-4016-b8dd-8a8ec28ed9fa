package com.think1024.tocodesign.ideaplugin.webview

import com.google.gson.JsonObject
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.constants.WindowIds
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

object WebViewBridge {
    /**
     * 发送标签页激活消息
     */
    @OptIn(DelicateCoroutinesApi::class)
    fun sendTabActivated(
        project: Project,
        toolWindowId: String = WindowIds.TOCO_DESIGN,
        tabUrl: String,
        tabTitle: String,
        tabType: String,
        isBinary: Boolean = false,
    ) {
        GlobalScope.launch {
            try {
                sendIfReady(
                    project,
                    toolWindowId,
                    "tab-activated",
                    mapOf(
                        "url" to tabUrl,
                        "title" to tabTitle,
                        "type" to tabType,
                        "isBinary" to isBinary
                    )
                )
            } catch (e: Exception) {
                println("❌ 发送标签页激活消息失败: ${e.message}")
            }
        }
    }


    /**
     * 向 WebView 发送消息（仅在 WebView 已加载的前提下）
     */
    @OptIn(DelicateCoroutinesApi::class)
    fun sendIfReady(
        project: Project,
        toolWindowId: String = WindowIds.TOCO_DESIGN, // 默认主窗口
        type: String,
        body: Any = emptyMap<String, Any>(),
    ) {
        GlobalScope.launch {
            try {
                doSend(project, toolWindowId, type, body, false)
            } catch (e: Exception) {
                println("❌ WebView sendIfReady 失败: ${e.message}")
            }
        }
    }

    /**
     * 向 WebView 发送消息，并等待响应
     */
    suspend fun sendAndWait(
        project: Project,
        toolWindowId: String = "TocoDesign",
        type: String,
        body: Any = emptyMap<String, Any>(),
    ): JsonObject? {
        return doSend(project, toolWindowId, type, body, true)
    }

    private suspend fun doSend(
        project: Project,
        toolWindowId: String = "TocoDesign",
        type: String,
        body: Any = emptyMap<String, Any>(),
        needResponse: Boolean? = false,
    ): JsonObject? {
        val browser = try {
            TocoBrowserManager.getInstance(project).getBrowser(toolWindowId)
        } catch (e: Exception) {
            println("❌ WebView 尚未初始化: ${e.message}")
            return null
        }

        return browser.getBrowser().sendToWebview(type, body, needResponse)
    }
}

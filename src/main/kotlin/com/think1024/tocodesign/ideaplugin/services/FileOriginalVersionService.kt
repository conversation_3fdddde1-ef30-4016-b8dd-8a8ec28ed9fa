package com.think1024.tocodesign.ideaplugin.services

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.diff.DiffContentFactory
import com.intellij.diff.DiffManager
import com.intellij.diff.requests.SimpleDiffRequest
import com.think1024.tocodesign.ideaplugin.toco.TOCO_ORIGINAL_DIFF_KEY
import com.think1024.tocodesign.ideaplugin.utils.FileUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import java.util.concurrent.ConcurrentHashMap

/**
 * 存储文件原始版本的服务类
 */
@Service(Service.Level.PROJECT)
class FileOriginalVersionService(private val project: Project) {
    private val logger = Logger.getInstance(FileOriginalVersionService::class.java)
    private val fileVersions = ConcurrentHashMap<String, String>()
    // 记录新建文件的集合
    private val newFiles = ConcurrentHashMap.newKeySet<String>()

    /**
     * 记录文件的原始版本（如果尚未记录）
     *
     * @param filePath 文件路径
     * @return 是否成功记录（如果已存在记录则返回false）
     */
    fun recordOriginalVersion(filePath: String): Boolean {
        // 如果已经记录过该文件的原始版本，则不再记录
        if (fileVersions.containsKey(filePath)) {
            return false
        }

        // 获取文件当前内容
        val codeFileService = CodeFileService.getInstance(project)
        // 如果拿不到内容，意味着新文件
        val content = codeFileService.getFileContent(filePath)

        // 如果是null，标记为新建文件
        if (content == null) {
            newFiles.add(filePath)
            logger.info("标记为新建文件: $filePath")
        }

        // 记录原始版本
        fileVersions[filePath] = content ?: ""
        logger.info("记录文件原始版本: $filePath")
        logger.info("记录文件原始内容: ${content ?: ""}")

        return true
    }

    /**
     * 判断文件是否为新建文件
     *
     * @param filePath 文件路径
     * @return 是否为新建文件
     */
    fun isNewFile(filePath: String): Boolean {
        return newFiles.contains(filePath)
    }

    /**
     * 手动标记文件为新建文件
     *
     * @param filePath 文件路径
     */
    fun markAsNewFile(filePath: String) {
        newFiles.add(filePath)
        // 如果尚未记录原始版本，记录为空内容
        if (!fileVersions.containsKey(filePath)) {
            fileVersions[filePath] = ""
        }
        logger.info("手动标记为新建文件: $filePath")
    }

    /**
     * 获取所有新建文件的路径列表
     *
     * @return 新建文件路径集合
     */
    fun getNewFiles(): Set<String> {
        return newFiles.toSet()
    }

    /**
     * 清除指定文件的记录（包括原始版本和新建文件标记）
     *
     * @param filePath 文件路径
     */
    fun clearFileRecord(filePath: String) {
        fileVersions.remove(filePath)
        newFiles.remove(filePath)
        logger.info("清除文件记录: $filePath")
    }

    /**
     * 清除所有记录
     */
    fun clearAllRecords() {
        fileVersions.clear()
        newFiles.clear()
        logger.info("清除所有文件记录")
    }

    /**
     * 获取文件的原始版本内容
     *
     * @param filePath 文件路径
     * @return 原始版本内容，如果不存在则返回null
     */
    fun getOriginalVersion(filePath: String): String? {
        return fileVersions[filePath]
    }

    /**
     * 显示指定文件的原始版本和当前版本的差异
     *
     * @param filePath 文件路径
     * @return 是否成功显示差异（如果没有原始版本则返回false）
     */
    fun showOriginalDiff(filePath: String, originContent: String? = null, commitId: String?): Boolean {
        // 获取原始版本 (优先从originContent获取，然后根据commitId去获取，最后从fileVersions获取)
        val readOriginContent = originContent ?: if (commitId != null) {
            val fossilService = FossilService.getInstance(project)
            fossilService.getFileContentByCommitId(filePath, commitId)
        } else {
            fileVersions[filePath]
        } ?: return false

        // 获取当前版本
        val codeFileService = CodeFileService.getInstance(project)
        val currentContent = codeFileService.getFileContent(filePath) ?: return false
        val virtualFile = FileUtil.getVirtualFile(filePath, project)

        // 显示差异
        ApplicationManager.getApplication().invokeLater {
            // 使用文件的实际Document作为右侧内容，这样编辑后可以直接保存
            val content2 = if (virtualFile != null) {
                DiffContentFactory.getInstance().create(project, virtualFile)
            } else {
                // 如果没有虚拟文件，创建一个临时的可编辑内容
                val document = com.intellij.openapi.editor.EditorFactory.getInstance().createDocument(currentContent)
                DiffContentFactory.getInstance().create(project, document, null as com.intellij.openapi.fileTypes.FileType?)
            }

            val diffRequest = SimpleDiffRequest(
                "${getI18nString("code.diff.title.review.diff")}: ${FileUtil.getVirtualFile(filePath, project)?.name ?: filePath}",
                DiffContentFactory.getInstance().create(readOriginContent),
                content2,
                getI18nString("code.diff.origin.version"),
                getI18nString("code.diff.current.version"),
            )

            // 标记这是原始版本diff，需要右侧可编辑
            diffRequest.putUserData(TOCO_ORIGINAL_DIFF_KEY, true)

            DiffManager.getInstance().showDiff(project, diffRequest)
        }

        return true
    }

    /**
     * 在源文件窗口中显示内联diff
     *
     * @param filePath 文件路径
     * @return 是否成功显示内联diff
     */
    fun showInlineDiff(filePath: String, originContent: String? = null): Boolean {
        // 获取原始版本
        val readOriginContent = originContent ?: fileVersions[filePath] ?: return false

        if (originContent != null && fileVersions[filePath] == null) {
            fileVersions[filePath] = originContent
        }

        // 使用内联diff服务
        val inlineDiffService = project.service<InlineDiffService>()
        return inlineDiffService.showInlineDiff(filePath, readOriginContent)
    }

    /**
     * 清除指定文件的原始版本记录
     */
    fun clearOriginalVersion(filePath: String) {
        fileVersions.remove(filePath)
        newFiles.remove(filePath)
    }

    /**
     * 判断文件是否有原始版本记录
     */
    fun hasOriginalVersion(filePath: String): Boolean {
        return fileVersions.containsKey(filePath)
    }

    companion object {
        /**
         * 获取服务实例
         */
        fun getInstance(project: Project): FileOriginalVersionService = project.service()
    }
}
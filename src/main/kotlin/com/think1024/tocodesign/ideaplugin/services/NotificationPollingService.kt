package com.think1024.tocodesign.ideaplugin.services

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindowManager
import com.intellij.openapi.util.IconLoader
import com.intellij.platform.project.projectId
import com.think1024.tocodesign.ideaplugin.constants.WindowIds
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.BadgeIconUtil
import com.think1024.tocodesign.ideaplugin.utils.HttpUtil
import com.think1024.tocodesign.ideaplugin.webview.WebViewBridge
import kotlinx.coroutines.*
import org.json.JSONObject
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * 轮询服务，用于监控long值的变化并在TOCO_DESIGN工具窗口显示红点提示
 */
@Service(Service.Level.PROJECT)
class NotificationPollingService(private val project: Project) {
    private val logger = Logger.getInstance(NotificationPollingService::class.java)

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var pollingJob: Job? = null

    // 当前监控的值
    private val currentValue = AtomicLong(0)
    // 是否有未读通知
    private val hasUnreadNotification = AtomicBoolean(false)

    companion object {
        fun getInstance(project: Project): NotificationPollingService {
            return project.getService(NotificationPollingService::class.java)
        }

        // 轮询间隔（毫秒）
        private const val POLLING_INTERVAL = 5000L // 5秒轮询
    }

    /**
     * 启动轮询
     */
    fun startPolling() {
        if (pollingJob?.isActive == true) {
            logger.info("Polling is already active")
            return
        }

        logger.info("Starting notification polling service")
        pollingJob = scope.launch {
            while (isActive) {
                try {
                    val newValue = fetchNotificationValue()
                    val oldValue = currentValue.get()

                    if (newValue != 0L) {
                        // 取得合法的新值
                        if (oldValue != 0L && newValue != oldValue) {
                            // 值发生变化，显示红点
                            logger.info("Notification value changed from $oldValue to $newValue")
                            showNotificationDot()

                            // 通知TocoDesign对应的前端页面去刷新设计元素列表
                            WebViewBridge.sendIfReady(project, WindowIds.TOCO_DESIGN, "refresh-design-items")
                        }

                        currentValue.set(newValue)
                    }

                    delay(POLLING_INTERVAL)
                } catch (e: Exception) {
                    logger.warn("Error during polling", e)
                    delay(POLLING_INTERVAL)
                }
            }
        }
    }

    /**
     * 停止轮询
     */
    fun stopPolling() {
        logger.info("Stopping notification polling service")
        pollingJob?.cancel()
        pollingJob = null
    }

    /**
     * 获取通知值（这里需要根据实际的API调用来实现）
     * TODO: 替换为实际的API调用
     */
    private fun fetchNotificationValue(): Long {
        // 这里应该是实际的网络请求或其他获取long值的逻辑
//        // 目前返回一个示例值
//        return System.currentTimeMillis() / 10000 // 每10秒变化一次，用于测试


        try {
            // 发送网络请求，url为/api/project/latestDraftVersion
            val host = ApplicationPluginSettings.getInstance().host
            val cookies = ApplicationPluginSettings.getInstance().cookies
            val projectId = ProjectPluginSettings.getInstance(project).projectId
            val response = HttpUtil.post(
                "$host/api/project/latestDraftVersion",
                cookies,
                "",
                mapOf("Projectid" to projectId as String)
            )
            val jsonResponse = JSONObject(response)
            if (jsonResponse.get("code") == 200 ) {
                val version = jsonResponse.optLong("data", 0L)
                return version
            }
            return 0L
        } catch (e: Exception) {
            logger.warn("Failed to fetch notification value", e)
            return 0L
        }
    }
    /**
     * 显示红点提示
     */
    private fun showNotificationDot() {
        ApplicationManager.getApplication().invokeLater {
            val toolWindowManager = ToolWindowManager.getInstance(project)
            val toolWindow = toolWindowManager.getToolWindow(WindowIds.TOCO_DESIGN)

            if (toolWindow != null && !toolWindow.isVisible) {
                hasUnreadNotification.set(true)
                // 设置工具窗口图标上的红点提示
                // 读取当前tool window 的图标
                val currentIcon = toolWindow.icon ?: IconLoader.getIcon("/icons/toco.svg", javaClass)
                toolWindow.setIcon(BadgeIconUtil.createBadgedIcon(currentIcon))
                logger.info("Notification dot shown on TOCO_DESIGN tool window")
            } else {
                logger.warn("TOCO_DESIGN tool window not found")
            }
        }
    }

    /**
     * 清除红点提示（当用户点击工具窗口时调用）
     */
    fun clearNotificationDot() {
        ApplicationManager.getApplication().invokeLater {
            if (hasUnreadNotification.compareAndSet(true, false)) {
                val toolWindowManager = ToolWindowManager.getInstance(project)
                val toolWindow = toolWindowManager.getToolWindow(WindowIds.TOCO_DESIGN)

                if (toolWindow != null) {
                    // 恢复原始图标
                    toolWindow.setIcon(IconLoader.getIcon("/icons/toco.svg", javaClass))
                    logger.info("Notification dot cleared from TOCO_DESIGN tool window")
                } else {
                    logger.warn("TOCO_DESIGN tool window not found when clearing notification")
                }
            }
        }
    }

    /**
     * 服务销毁时停止轮询
     */
    fun dispose() {
        stopPolling()
        scope.cancel()
    }
}

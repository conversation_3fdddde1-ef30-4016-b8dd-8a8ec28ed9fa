package com.think1024.tocodesign.ideaplugin.services

import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.toco.Fossil
import com.think1024.tocodesign.ideaplugin.toco.FossilCmd
import com.think1024.tocodesign.ideaplugin.utils.FileUtil

@Service(Service.Level.PROJECT)
class FossilService(private val project: Project) {
    private val logger = Logger.getInstance(FossilService::class.java)

    data class CommitFilesRequest(
        val workingFolder: String? = null,
        val comment: String? = null,
        val bot: Boolean? = null,
        val ignores: List<String>? = null
    )

    data class CommitFilesResult(
        val success: Boolean,
        val commitId: String? = null,
        val message: String? = null
    )

    data class CommitFileRequest(
        val file: Any, // 可以是String或List<String>
        val workingFolder: String? = null,
        val comment: String? = null,
        val bot: Boolean? = null
    )

    data class CommitFileResult(
        val success: Boolean,
        val commitId: String? = null,
        val message: String? = null
    )

    data class GetLatestCommitRequest(
        val workingFolder: String? = null,
        val branch: String? = null
    )

    data class GetLatestCommitResult(
        val success: Boolean,
        val commitId: String? = null,
        val message: String? = null
    )

    /**
     * 提交文件到Fossil版本控制系统
     *
     * @param request 提交请求参数
     * @return 提交结果
     */
    fun commitAllFiles(request: CommitFilesRequest): CommitFilesResult {
        return try {
            // 使用项目根路径作为默认工作目录
            val workingFolder = request.workingFolder ?: project.basePath ?: ""

            // 使用默认提交注释
            val comment = request.comment ?: "Auto commit from IDE"

            // 默认不是机器人提交
            val bot = request.bot ?: true

            // 调用FossilCmd.commitFiles
            val commitId = FossilCmd.commitFiles(workingFolder, comment, bot, request.ignores)

            if (commitId != null) {
                logger.info("Successfully committed files with ID: $commitId")
                CommitFilesResult(success = true, commitId = commitId)
            } else {
                logger.warn("Commit failed - no commit ID returned")
                CommitFilesResult(success = false, message = "Commit operation failed")
            }

        } catch (e: Exception) {
            logger.error("Error during commit operation", e)
            CommitFilesResult(
                success = false,
                message = e.message ?: "Unknown error occurred during commit"
            )
        }
    }

    /**
     * 提交单个或多个特定文件到Fossil版本控制系统
     *
     * @param request 提交文件请求参数
     * @return 提交结果
     */
    fun commitFile(request: CommitFileRequest): CommitFileResult {
        return try {
            // 使用项目根路径作为默认工作目录
            val workingFolder = request.workingFolder ?: project.basePath ?: ""

            // 使用默认提交注释
            val comment = request.comment ?: "Add file from IDE"

            // 默认不是机器人提交
            val bot = request.bot ?: true

            // 调用FossilCmd.addFile
            val commitId = FossilCmd.addFile(request.file, workingFolder, comment, bot)

            if (commitId != null) {
                logger.info("Successfully added and committed file(s) with ID: $commitId")
                CommitFileResult(success = true, commitId = commitId)
            } else {
                logger.warn("Add file failed - no commit ID returned")
                CommitFileResult(success = false, message = "Add file operation failed")
            }

        } catch (e: Exception) {
            logger.error("Error during add file operation", e)
            CommitFileResult(
                success = false,
                message = e.message ?: "Unknown error occurred during add file"
            )
        }
    }

    /**
     * 提交单个文件的简化版本
     *
     * @param filePath 文件路径
     * @param comment 提交注释
     * @return 提交结果
     */
    fun commitFile(filePath: String, comment: String = "Add file from IDE"): CommitFileResult {
        return commitFile(CommitFileRequest(file = filePath, comment = comment))
    }

    /**
     * 获取项目的工作目录
     */
    fun getWorkingFolder(): String {
        return project.basePath ?: ""
    }

    /**
     * rollback到指定commit
     */
    fun rollbackToCommit(commitId: String, fileList: Array<String>?): Boolean {
        if (commitId == "") {
            return false
        }
        logger.info("start invoke rollback")
        val result = FossilCmd.rollback(getWorkingFolder(), commitId, false)
        if (result && project.basePath != null) {
            logger.info("start invoke cleanupBranchesWithInitialProjectAncestor")
            Fossil.cleanupBranchesWithInitialProjectAncestor(project.basePath as String, project.name)
        }
        logger.info("finish invoke rollback")
        return result
    }

    /**
     * 获取最新的commit信息
     *
     * @param request 获取最新commit请求参数
     * @return 获取结果
     */
    fun getLatestCommit(request: GetLatestCommitRequest): GetLatestCommitResult {
        return try {
            // 使用项目根路径作为默认工作目录
            val workingFolder = request.workingFolder ?: project.basePath ?: ""

            // 调用FossilCmd.getLatestCommit
            val commit = FossilCmd.getLatestCommit(workingFolder, request.branch)

            if (commit != null) {
                logger.info("Successfully retrieved latest commit: ${commit.commitId}")
                GetLatestCommitResult(success = true, commitId = commit.commitId)
            } else {
                logger.warn("No latest commit found")
                GetLatestCommitResult(success = false, message = "No latest commit found")
            }

        } catch (e: Exception) {
            logger.error("Error during get latest commit operation", e)
            GetLatestCommitResult(
                success = false,
                message = e.message ?: "Unknown error occurred while getting latest commit"
            )
        }
    }

    /**
     * 获取最新commit的简化版本
     *
     * @param branch 分支名称，可选
     * @return 获取结果
     */
    fun getLatestCommit(branch: String? = null): GetLatestCommitResult {
        return getLatestCommit(GetLatestCommitRequest(branch = branch))
    }

    fun getFileContentByCommitId(filePath: String, commitId: String?): String? {
        // 把filePath转为绝对路径
        val absolutePath = FileUtil.normalizeAbsolutePath(filePath, project)
        return FossilCmd.cat(absolutePath, commitId, getWorkingFolder())
    }

    companion object {
        fun getInstance(project: Project): FossilService = project.getService(FossilService::class.java)
    }
}

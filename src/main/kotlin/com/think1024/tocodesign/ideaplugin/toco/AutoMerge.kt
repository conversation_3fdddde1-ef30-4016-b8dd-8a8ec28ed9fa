package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.diff.DiffContext
import com.intellij.diff.DiffRequestFactory
import com.intellij.diff.merge.*
import com.intellij.diff.merge.MergeResult
import com.intellij.diff.requests.ContentDiffRequest
import com.intellij.diff.tools.util.base.DiffViewerListener
import com.intellij.diff.tools.util.base.IgnorePolicy
import com.intellij.diff.tools.util.base.TextDiffSettingsHolder.TextDiffSettings
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.editor.Document
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileTypes.FileType
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Condition
import com.intellij.testFramework.LightVirtualFile
import com.intellij.util.containers.ContainerUtil

data class AutoMergeOptions(
    val type: FileType,
    val base: String,
    val ours: String,
    val theirs: String,
)

private class MockMergeContext(private val myProject: Project?) : MergeContext() {
    override fun getProject(): Project? = myProject

    override fun isFocusedInWindow(): Boolean = false

    override fun requestFocusInWindow() {
    }

    override fun finishMerge(result: MergeResult) {
    }
}

class MyMergeThreesideViewer(context: DiffContext, request: ContentDiffRequest, mergeContext: MergeContext, mergeRequest: TextMergeRequest, mergeViewer: TextMergeViewer, private val onAfterRediff: (MergeThreesideViewer) -> Unit): MergeThreesideViewer(context, request, mergeContext, mergeRequest, mergeViewer) {
    override fun doPerformRediff(indicator: ProgressIndicator): Runnable {
        val ret = super.doPerformRediff(indicator)
        onAfterRediff(this)
        return ret
    }
}

class MyTextMergeViewer(context: MergeContext, request: TextMergeRequest, private val onAfterRediff: (MergeThreesideViewer) -> Unit): TextMergeViewer(context, request) {

    override fun loadThreeSideViewer(
        context: DiffContext,
        request: ContentDiffRequest,
        mergeContext: MergeContext,
        mergeRequest: TextMergeRequest,
        mergeViewer: TextMergeViewer
    ): MergeThreesideViewer? {
        return MyMergeThreesideViewer(context, request, mergeContext, mergeRequest, mergeViewer) {
            onAfterRediff(it)
        }
    }
}

class AutoMerge(val project: Project, val options: AutoMergeOptions) {

    fun merge(callback: (String?) -> Unit) {
        var document: Document? = null
        ApplicationManager.getApplication().runReadAction {
            document = FileDocumentManager.getInstance().getDocument(LightVirtualFile("temp", options.type, "11111"))
        }
        if (document != null) {
            val doc = document!!
            val request = DiffRequestFactory.getInstance().createMergeRequest(
                project,
                options.type,
                doc,
                listOf(
                    options.ours,
                    options.base,
                    options.theirs
                ),
                "",
                listOf("", "", ""),
                null
            )

//            DiffManager.getInstance().showMerge(project, request)

            val context = MockMergeContext(project)
            val settings = TextDiffSettings()
            settings.ignorePolicy = IgnorePolicy.IGNORE_WHITESPACES_CHUNKS
            settings.isAutoResolveImportConflicts = true
            context.putUserData(TextDiffSettings.KEY, settings)

            ApplicationManager.getApplication().invokeLater {
                try {
                    val viewer = MyTextMergeViewer(context, request as TextMergeRequest) {
                        ApplicationManager.getApplication().invokeLater {
//                            it.applyResolvableConflictedChanges()
                            val firstUnresolved = ContainerUtil.find<TextMergeChange?>(
                                it.allChanges,
                                Condition { c: TextMergeChange? -> !c!!.isResolved() }) as TextMergeChange?
                            if (firstUnresolved == null) {
                                callback(doc.text)
                            } else {
                                callback(null)
                            }
                        }
                    }
                    viewer.viewer.init()
                    viewer.viewer.rediff(false)
                } catch (e: Exception) {
                    println(e)
                    callback(null)
                }
            }
        }
    }
}
# 外部客户环境配置 (public)
# Public customer environment configuration

# project info (public - different from internal)
project.plugin.id=com.think1024.tocodesign.ideaplugin
project.name=tocoai

# plugin display info
plugin.display.name=Toco Ai
plugin.vendor.url=https://tocoai.dev/
plugin.vendor.email=<EMAIL>

# desktop protocol (public specific)
locator.desktop.url.protocol=tocodesign
locator.desktop.url.protocol.test=tocodesigntest

# hosts (public - different domains)
url.host.default=https://toco-dev2.teitui.com
url.host.test=https://toco-dev2.teitui.com
url.frontend.host.default=https://toco-dev2.teitui.com
url.frontend.host.test=https://toco-dev2.teitui.com

package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.diagnostic.Logger
import java.io.InputStreamReader
import java.util.*

/**
 * 环境配置工具类
 * 支持两种环境配置：internal（包含runIde沙箱环境）、public
 */
object EnvironmentConfigUtil {
    private val logger = Logger.getInstance(EnvironmentConfigUtil::class.java)
    
    // 环境类型枚举
    enum class Environment(val configFile: String) {
        INTERNAL("internal.properties"),
        PUBLIC("public.properties")
    }
    
    // 配置缓存
    private val configCache = mutableMapOf<Environment, Properties>()
    private var commonConfig: Properties? = null
    
    // 当前环境，默认为INTERNAL（runIde沙箱环境也使用internal配置）
    private var currentEnvironment: Environment = Environment.INTERNAL
    
    init {
        // 初始化时加载通用配置
        loadCommonConfig()
        // 根据系统属性或环境变量确定当前环境
        determineCurrentEnvironment()
    }
    
    /**
     * 确定当前运行环境
     * 优先级：1. 构建时环境文件 2. 系统属性 3. 环境变量 4. 默认internal
     */
    private fun determineCurrentEnvironment() {
        // 首先尝试从构建时生成的环境文件读取
        val buildEnvironment = loadBuildEnvironment()

        // FIXME: 这里默认值暂时设置为内部包
        val envProperty = buildEnvironment ?: "internal"

        currentEnvironment = when (envProperty.lowercase()) {
            "public" -> Environment.PUBLIC
            else -> Environment.INTERNAL  // runIde、internal或其他情况都使用internal配置
        }

        logger.info("Current environment determined as: $currentEnvironment (source: ${if (buildEnvironment != null) "build-time" else "runtime"})")
    }

    /**
     * 从构建时生成的环境文件中加载环境信息
     */
    private fun loadBuildEnvironment(): String? {
        return try {
            val inputStream = javaClass.classLoader.getResourceAsStream("build-environment.properties")
            if (inputStream != null) {
                val props = Properties()
                InputStreamReader(inputStream, "UTF-8").use { reader ->
                    props.load(reader)
                }
                val environment = props.getProperty("build.environment")
                logger.info("Build environment loaded: $environment")
                environment
            } else {
                logger.debug("Build environment file not found, using runtime detection")
                null
            }
        } catch (e: Exception) {
            logger.warn("Failed to load build environment file", e)
            null
        }
    }
    
    /**
     * 加载通用配置
     */
    private fun loadCommonConfig() {
        try {
            val inputStream = javaClass.classLoader.getResourceAsStream("config/common.properties")
            if (inputStream != null) {
                commonConfig = Properties().apply {
                    InputStreamReader(inputStream, "UTF-8").use { reader ->
                        load(reader)
                    }
                }
                logger.info("Common config loaded successfully")
            } else {
                logger.warn("Common config file not found")
            }
        } catch (e: Exception) {
            logger.error("Failed to load common config", e)
        }
    }
    
    /**
     * 加载指定环境的配置
     */
    private fun loadEnvironmentConfig(environment: Environment): Properties {
        return configCache.getOrPut(environment) {
            val properties = Properties()
            try {
                val inputStream = javaClass.classLoader.getResourceAsStream("config/${environment.configFile}")
                if (inputStream != null) {
                    InputStreamReader(inputStream, "UTF-8").use { reader ->
                        properties.load(reader)
                    }
                    logger.info("Environment config loaded: ${environment.configFile}")
                } else {
                    logger.warn("Environment config file not found: ${environment.configFile}")
                }
            } catch (e: Exception) {
                logger.error("Failed to load environment config: ${environment.configFile}", e)
            }
            properties
        }
    }
    
    /**
     * 获取合并后的配置（通用配置 + 环境特定配置）
     */
    fun getMergedConfig(environment: Environment = currentEnvironment): Properties {
        val mergedConfig = Properties()
        
        // 先加载通用配置
        commonConfig?.let { mergedConfig.putAll(it) }
        
        // 再加载环境特定配置，覆盖通用配置中的相同项
        val envConfig = loadEnvironmentConfig(environment)
        mergedConfig.putAll(envConfig)
        
        return mergedConfig
    }
    
    /**
     * 获取配置属性值
     */
    fun getProperty(key: String, environment: Environment = currentEnvironment): String? {
        return getMergedConfig(environment).getProperty(key)
    }
    
    /**
     * 获取当前环境
     */
    fun getCurrentEnvironment(): Environment = currentEnvironment
    
    /**
     * 设置当前环境（主要用于测试）
     */
    fun setCurrentEnvironment(environment: Environment) {
        currentEnvironment = environment
        logger.info("Environment changed to: $environment")
    }
    
    /**
     * 清除配置缓存
     */
    fun clearCache() {
        configCache.clear()
        commonConfig = null
        loadCommonConfig()
        logger.info("Configuration cache cleared and reloaded")
    }
    
    /**
     * 获取所有配置属性（用于调试）
     */
    fun getAllProperties(environment: Environment = currentEnvironment): Map<String, String> {
        val config = getMergedConfig(environment)
        return config.stringPropertyNames().associateWith { config.getProperty(it) }
    }
}

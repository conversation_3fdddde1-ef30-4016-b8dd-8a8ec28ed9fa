package com.think1024.tocodesign.ideaplugin.services

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.progress.ProcessCanceledException
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.toco.TocoVirtualFile
import com.think1024.tocodesign.ideaplugin.toco.TocoWebViewEditor

/**
 * 服务类，用于刷新所有打开的 TocoVirtualFile
 */
@Service(Service.Level.PROJECT)
class TocoFileRefreshService(private val project: Project) {
    private val logger = Logger.getInstance(TocoFileRefreshService::class.java)

    /**
     * 刷新所有打开的 TocoVirtualFile
     * @return 刷新结果，包含成功刷新的文件数量和失败的文件数量
     */
    fun refreshAllTocoFiles(): Map<String, Any> {
        logger.info("开始刷新所有打开的 TocoVirtualFile")

        var successCount = 0
        var failureCount = 0
        val refreshedFiles = mutableListOf<String>()
        val failedFiles = mutableListOf<String>()

        try {
            // 在 EDT 线程中执行，确保线程安全
            ApplicationManager.getApplication().invokeAndWait {
                val fileEditorManager = FileEditorManager.getInstance(project)
                val openFiles = fileEditorManager.openFiles

                // 遍历所有打开的文件
                for (file in openFiles) {
                    if (file is TocoVirtualFile) {
                        try {
                            logger.info("找到 TocoVirtualFile: ${file.name}, URL: ${file.webUrl}")

                            // 获取对应的编辑器
                            val editors = fileEditorManager.getEditors(file)

                            for (editor in editors) {
                                if (editor is TocoWebViewEditor) {
                                    logger.info("刷新 TocoWebViewEditor: ${file.name}")
                                    editor.refresh()
                                    successCount++
                                    refreshedFiles.add(file.name)
                                    break // 每个文件只需要刷新一次
                                }
                            }
                        } catch (e: Exception) {
                            logger.error("刷新文件失败: ${file.name}", e)
                            failureCount++
                            failedFiles.add(file.name)
                        }
                    }
                }
            }
        } catch (e: ProcessCanceledException) {
            // ProcessCanceledException 必须重新抛出，不能被捕获和记录
            throw e
        } catch (e: Exception) {
            logger.error("刷新所有 TocoVirtualFile 时发生错误", e)
            return mapOf(
                "success" to false,
                "message" to "刷新过程中发生错误: ${e.message}",
                "successCount" to successCount,
                "failureCount" to failureCount,
                "refreshedFiles" to refreshedFiles,
                "failedFiles" to failedFiles
            )
        }

        logger.info("TocoVirtualFile 刷新完成: 成功 $successCount 个，失败 $failureCount 个")

        return mapOf(
            "success" to true,
            "message" to "刷新完成: 成功 $successCount 个文件，失败 $failureCount 个文件",
            "successCount" to successCount,
            "failureCount" to failureCount,
            "refreshedFiles" to refreshedFiles,
            "failedFiles" to failedFiles
        )
    }

    companion object {
        fun getInstance(project: Project): TocoFileRefreshService {
            return project.getService(TocoFileRefreshService::class.java)
        }
    }
}

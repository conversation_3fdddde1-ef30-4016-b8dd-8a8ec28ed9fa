package com.think1024.tocodesign.ideaplugin.services

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.ModalityState
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.Task
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.guessProjectDir
import com.intellij.openapi.vfs.VirtualFile
import com.think1024.tocodesign.ideaplugin.listeners.CompatibleMavenProjectsListener
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.utils.ModuleConfigUtil
import org.jetbrains.idea.maven.project.MavenProject
import org.jetbrains.idea.maven.project.MavenProjectsManager
import java.util.concurrent.ConcurrentHashMap

/**
 * Service responsible for reloading Maven projects and caching module configurations.
 * This service is registered at the project level.
 */
@Service(Service.Level.PROJECT)
class ModuleConfigService(private val project: Project) {
    private val logger = Logger.getInstance(ModuleConfigService::class.java)

    // Maintain a list of previously known modules
    private val knownModules: MutableSet<String> = ConcurrentHashMap.newKeySet()

    init {
        val projectSettings = ProjectPluginSettings.getInstance(project)

        // Automatically reload and cache modules if a project ID is set
        if (!projectSettings.projectId.isNullOrEmpty()) {
            reloadMavenProjectsAndCacheModules()
        }
    }

    /**
     * Reloads Maven projects and caches module configurations.
     * Executes these tasks in background threads to prevent UI blocking.
     */
    private fun reloadMavenProjectsAndCacheModules() {
        try {
            if (isPomFilePresent(project)) {
                // If pom.xml is present, treat it as a Maven project
                val mavenProjectsManager = MavenProjectsManager.getInstance(project)

                // Register a listener for Maven projects updates
                registerMavenProjectsListener(mavenProjectsManager)

                // Queue Maven project reload
                queueMavenProjectReload(mavenProjectsManager)
            } else {
                // If no pom.xml is found, treat it as a non-Maven project
                logger.info("No pom.xml found. Treating as non-Maven project: ${project.name}")
                // Directly scan and cache modules for non-Maven projects
                invokeLater { ModuleConfigUtil.scanAndCacheModules(project) }
            }
        } catch (e: Exception) {
            // Log errors during Maven project reload and module caching
            logger.warn("Failed to reload Maven projects and scan/cache modules for project: ${project.name}", e)
        }
    }

    /**
     * Checks if a pom.xml file is present in the project's base directory.
     *
     * @param project The project to check for pom.xml
     * @return true if pom.xml is found, false otherwise
     */
    private fun isPomFilePresent(project: Project): Boolean {
        val projectDir: VirtualFile? = project.guessProjectDir()
        return projectDir?.findChild("pom.xml") != null
    }

    /**
     * Registers a listener for updates in the specified MavenProjectsManager.
     * This method uses CompatibleMavenProjectsListener to handle version differences across IntelliJ IDEA 2022.2 to 2024.3.
     *
     * @param mavenProjectsManager The MavenProjectsManager instance to register the listener with.
     */
    private fun registerMavenProjectsListener(mavenProjectsManager: MavenProjectsManager) {
        try {
            // Create a new CompatibleMavenProjectsListener instance
            val listenerWrapper = CompatibleMavenProjectsListener(project) { updated, deleted ->
                // updated: List of pairs containing updated MavenProject and their changes
                // deleted: List of deleted MavenProjects

                // Ensure UI updates are performed on the Event Dispatch Thread
                invokeLater {
                    // Extract the current projects from the updated list
                    val currentProjects = updated.map { it.first }

                    // Check if the Maven project structure has changed
                    val projectChanged = detectMavenProjectChanges(currentProjects, knownModules)

                    if (projectChanged) {
                        logger.info("Maven project structure has changed.")

                        // Perform necessary operations, such as rescanning and caching modules
                        ModuleConfigUtil.scanAndCacheModules(project)

                        logger.info("Module configuration scan and cache completed for project: ${project.name}")
                    }
                }
            }

            // Register the listener using the dynamic proxy instance
            // This approach allows for compatibility across different IntelliJ IDEA versions
            mavenProjectsManager.addProjectsTreeListener(listenerWrapper.proxyInstance)
        } catch (e: Exception) {
            // Log any errors that occur during listener registration
            logger.warn("Failed to register Maven projects listener for project: ${project.name}", e)
        }
    }

    /**
     * Compares the current Maven projects with the known modules to detect any changes.
     * Updates the known modules to the current state if changes are detected.
     *
     * @param currentProjects The current list of Maven projects.
     * @param knownModules Mutable set of previously known module paths.
     * @return True if changes are detected, false otherwise.
     */
    private fun detectMavenProjectChanges(
        currentProjects: List<MavenProject>,
        knownModules: MutableSet<String>
    ): Boolean {
        // Create a new set for the current module paths
        val currentModules = currentProjects.map { it.path }.toSet()

        // Check if there are any differences between current and known modules
        val hasChanges = currentModules != knownModules

        if (hasChanges) {
            // Update known modules to the current state
            knownModules.clear()  // Clear previous known modules
            knownModules.addAll(currentModules)  // Add current modules to known modules
        }

        return hasChanges
    }

    /**
     * Queues a background task to reload all Maven projects and update module configurations.
     * This method ensures that module configurations are updated immediately after Maven projects are reloaded.
     *
     * @param mavenProjectsManager The MavenProjectsManager instance used to manage Maven projects.
     */
    private fun queueMavenProjectReload(mavenProjectsManager: MavenProjectsManager) {
        ApplicationManager.getApplication().invokeLater({
            object : Task.Backgroundable(project, "Reloading Maven Projects", false) {
                override fun run(indicator: ProgressIndicator) {
                    try {
                        // Force update all Maven projects
                        val projects = mavenProjectsManager.projects
                        if (projects.isEmpty()) {
                            mavenProjectsManager.forceUpdateAllProjectsOrFindAllAvailablePomFiles()
                            logger.info("Maven projects reload triggered for project: ${project.name}")
                        } else {
                            detectMavenProjectChanges(projects, knownModules)
                            // Update module configurations immediately after Maven reload
                            ApplicationManager.getApplication().invokeLater({
                                ModuleConfigUtil.scanAndCacheModules(project)
                                logger.info("Module configurations updated after Maven reload for project: ${project.name}")
                            }, ModalityState.NON_MODAL)
                        }
                    } catch (e: Exception) {
                        logger.warn(
                            "Failed to reload Maven projects or update module configurations for project: ${project.name}",
                            e
                        )
                    }
                }
            }.queue()
        }, ModalityState.NON_MODAL)
    }

    /**
     * Invokes an action later on the application thread.
     */
    private fun invokeLater(action: () -> Unit) {
        try {
            ApplicationManager.getApplication().invokeLater(action, ModalityState.NON_MODAL)
        } catch (e: Exception) {
            logger.warn("Error while invoking action later for project: ${project.name}", e)
        }
    }
}

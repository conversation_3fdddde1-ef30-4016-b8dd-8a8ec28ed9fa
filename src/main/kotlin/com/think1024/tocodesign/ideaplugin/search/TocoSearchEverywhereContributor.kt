package com.think1024.tocodesign.ideaplugin.search

import com.intellij.icons.AllIcons
import com.intellij.ide.actions.searcheverywhere.SearchEverywhereContributor
import com.intellij.ide.actions.searcheverywhere.SearchEverywhereContributorFactory
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.IconLoader
import com.intellij.ui.ColoredListCellRenderer
import com.intellij.ui.SimpleColoredComponent
import com.intellij.ui.SimpleTextAttributes
import com.intellij.util.IconUtil
import com.intellij.util.Processor
import com.intellij.util.ui.UIUtil
import com.think1024.tocodesign.ideaplugin.constants.WindowIds
import com.think1024.tocodesign.ideaplugin.services.TocoService
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.toco.TocoWebViewLauncher
import com.think1024.tocodesign.ideaplugin.webview.WebViewBridge
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import okhttp3.internal.notifyAll
import okhttp3.internal.wait
import org.json.JSONArray
import java.awt.BorderLayout
import java.awt.Component
import java.awt.Font
import javax.swing.Icon
import javax.swing.JList
import javax.swing.JPanel
import javax.swing.ListCellRenderer

class TocoSearchEverywhereContributor(private val project: Project?) : SearchEverywhereContributor<TocoSearchItem> {

    override fun getSearchProviderId(): String = "toco"

    override fun getGroupName(): String = "Toco"

    override fun getSortWeight(): Int = 1000

    fun getIcon(): Icon = IconLoader.getIcon("/icons/toco.svg", this.javaClass)

    override fun isShownInSeparateTab(): Boolean = true

    override fun showInFindResults(): Boolean = true

    override fun getElementsRenderer(): ListCellRenderer<in TocoSearchItem> {
        return object : ColoredListCellRenderer<TocoSearchItem>() {
            override fun getListCellRendererComponent(
                list: JList<out TocoSearchItem?>,
                value: TocoSearchItem?,
                index: Int,
                selected: Boolean,
                hasFocus: Boolean
            ): Component {
                var cmp = super.getListCellRendererComponent(list, value, index, selected, hasFocus)
                if (value is TocoSearchItem) {
                    val panel = JPanel(BorderLayout())
                    panel.setBackground(UIUtil.getListBackground(selected, true))
                    panel.add(cmp, "Center")
                    val label = SimpleColoredComponent()
                    label.append(value.extraInfo ?: "", SimpleTextAttributes.GRAYED_ATTRIBUTES)
                    label.font = Font(UIUtil.getLabelFont().name, Font.PLAIN, 12)
                    panel.add(label, "East")
                    cmp = panel
                }
                return cmp
            }
            override fun customizeCellRenderer(
                list: JList<out TocoSearchItem?>,
                value: TocoSearchItem?,
                index: Int,
                selected: Boolean,
                hasFocus: Boolean
            ) {
                ipad.right = 2
                ipad.left = 2
                icon = IconUtil.toSize(value?.icon ?: IconLoader.getIcon("/icons/toco_color.svg", this.javaClass), 16, 16)
                append(value?.name ?: "")
                value?.description?.let {
                    append(" $it", SimpleTextAttributes.GRAYED_ATTRIBUTES)
                }
            }

        }
    }

    override fun fetchElements(
        pattern: String,
        progressIndicator: ProgressIndicator,
        consumer: Processor<in TocoSearchItem>
    ) {
        if (pattern.isEmpty() || project == null) return

        val logger = Logger.getInstance(this.javaClass)
        logger.info("Starting search for: $pattern")

        // 创建同步锁对象
        val lock = Any()
        var items: List<TocoSearchItem>? = null
        var error: Throwable? = null

        // 在后台线程执行API请求（但不切换上下文）
        ApplicationManager.getApplication().executeOnPooledThread {
            synchronized(lock) {
                try {
                    val projectId = ProjectPluginSettings.getInstance(project).projectId ?: ""
                    val (response, _) = TocoService.get(
                        "url.path.search",
                        mapOf("s" to pattern, "type" to ""),
                        mapOf("projectId" to projectId),
                    )

                    items = if (response != null) {
                        parseSearchResults(response, pattern)
                    } else {
                        listOf()
                    }
                } catch (e: Exception) {
                    logger.error("Exception during search", e)
                    error = e
                    items = listOf()
                } finally {
                    // 唤醒等待的线程
                    lock.notifyAll()
                }
            }
        }

        // 同步等待结果（但不阻塞UI线程）
        synchronized(lock) {
            while (items == null) {
                try {
                    lock.wait()
                } catch (_: InterruptedException) {
                    Thread.currentThread().interrupt()
                    return
                }
            }
        }

        // 在原始线程上下文中处理结果
        items?.forEach { item ->
            if (progressIndicator.isCanceled) return
            consumer.process(item)
        }
    }


    /**
     * 解析搜索结果
     */
    private fun parseSearchResults(response: Any, query: String): List<TocoSearchItem> {
        val items = mutableListOf<TocoSearchItem>()
        val tocoIcon = IconLoader.getIcon("/icons/toco.svg", this.javaClass)

        // 定义图标映射
        val iconMap: Map<String, Icon> = mapOf(
            "project-home" to IconLoader.getIcon("/icons/project-home.svg", this.javaClass), // project-home 对应的图标
            "module-home" to IconLoader.getIcon("/icons/module-home.svg", this.javaClass), // module-home 对应的图标
            "module" to IconLoader.getIcon("/icons/menu/s-module.svg", this.javaClass), // module 对应的图标
            "entity" to IconLoader.getIcon("/icons/menu/e.svg", this.javaClass), // entity 对应的图标
            "api" to IconLoader.getIcon("/icons/api.svg", this.javaClass), // api 对应的图标
            "bo" to IconLoader.getIcon("/icons/bo.svg", this.javaClass), // bo 对应的图标
            "bo_definition" to IconLoader.getIcon("/icons/bo.svg", this.javaClass), // bo 对应的图标
            "dto" to IconLoader.getIcon("/icons/dto.svg", this.javaClass), // dto 对应的图标
            "enum" to IconLoader.getIcon("/icons/enum.svg", this.javaClass), // enum 对应的图标
            "eo" to IconLoader.getIcon("/icons/eo.svg", this.javaClass), // eo 对应的图标
            "er" to IconLoader.getIcon("/icons/er.svg", this.javaClass), // er 对应的图标
            "flow" to IconLoader.getIcon("/icons/flow.svg", this.javaClass), // flow 对应的图标
            "msg" to IconLoader.getIcon("/icons/msg.svg", this.javaClass), // msg 对应的图标
            "read" to IconLoader.getIcon("/icons/read.svg", this.javaClass), // read 对应的图标
            "rpc" to IconLoader.getIcon("/icons/rpc.svg", this.javaClass), // rpc 对应的图标
            "vo" to IconLoader.getIcon("/icons/vo.svg", this.javaClass), // vo 对应的图标
            "new_vo" to IconLoader.getIcon("/icons/vo.svg", this.javaClass), // vo 对应的图标
            "write" to IconLoader.getIcon("/icons/write.svg", this.javaClass), // write 对应的图标
        )

        try {
            when (response) {
                is JSONArray -> {
                    // 假设响应是一个 JSONArray
                    for (i in 0 until response.length()) {
                        val item = response.getJSONObject(i)
                        if (item != null) {
                            val type = item.optString("type", null).lowercase()
                            val icon = iconMap[type]

                            items.add(
                                TocoSearchItem(
                                    name = item.optString("name"),
                                    description = item.optString("cname", ""),
                                    icon = icon,
                                    extraInfo = item.optString("type", null) + " < " + item.optString("moduleName", null) + " >",
                                    origin = item
                                )
                            )
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // 解析错误
            items.add(
                TocoSearchItem(
                    name = "Error parsing results for: $query",
                    description = "Error: ${e.message}",
                    icon = tocoIcon
                )
            )
        }

        // 如果没有结果，添加一个提示
        if (items.isEmpty()) {
            items.add(
                TocoSearchItem(
                    name = "No results for: $query",
                    description = "Try a different search term",
                    icon = tocoIcon
                )
            )
        }

        return items
    }

    override fun getDataForItem(element: TocoSearchItem, dataId: String): Any? = null

    @OptIn(DelicateCoroutinesApi::class)
    override fun processSelectedItem(selected: TocoSearchItem, modifiers: Int, searchText: String): Boolean {
        // 处理选中的项目，例如打开相关文件或执行操作
        println("Selected Toco item: ${selected.name}")
        if (project !== null) {
            GlobalScope.launch {
                try {
                    WebViewBridge.sendIfReady(project, WindowIds.TOCO_MENU,"select-search-item", selected.origin ?: {})
                } catch (e: Exception) {
                    println("❌ WebView 请求失败: ${e.message}")
                }
            }
        }
        return true
    }

    class Factory : SearchEverywhereContributorFactory<TocoSearchItem> {
        override fun createContributor(initEvent: AnActionEvent): SearchEverywhereContributor<TocoSearchItem> {
            return TocoSearchEverywhereContributor(initEvent.project)
        }

        override fun isAvailable(project: Project) = ProjectPluginSettings.getInstance(project).projectId != null
    }
}

// 定义搜索项目类
data class TocoSearchItem(
    val name: String,
    val description: String,
    val icon: Icon? = null,
    val extraInfo: String? = null,
    val origin: Any? = null
)

package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.diagnostic.Logger
import java.io.InputStreamReader
import java.util.*

/**
 * Utility object for reading configuration properties.
 * 现在使用环境配置系统，支持多环境配置
 */
object ConfigUtil {
    private val logger = Logger.getInstance(ConfigUtil::class.java)
    private val properties: Properties = Properties()
    private val fallbackProperties: Properties = Properties()

    init {
        // 首先尝试使用新的环境配置系统
        try {
            val envConfig = EnvironmentConfigUtil.getMergedConfig()
            properties.putAll(envConfig)
            logger.info("Loaded environment-based configuration")
        } catch (e: Exception) {
            logger.warn("Failed to load environment configuration, falling back to legacy config.properties", e)
            loadFallbackConfig()
        }

        // 如果环境配置为空，使用fallback配置
        if (properties.isEmpty) {
            loadFallbackConfig()
        }
    }

    /**
     * 加载传统的config.properties作为fallback
     */
    private fun loadFallbackConfig() {
        val inputStream = ConfigUtil::class.java.classLoader.getResourceAsStream("config.properties")
        if (inputStream == null) {
            logger.warn("config.properties not found")
        } else {
            try {
                InputStreamReader(inputStream, "UTF-8").use { reader ->
                    fallbackProperties.load(reader)
                    properties.putAll(fallbackProperties)
                }
                logger.info("Loaded fallback properties: ${properties.stringPropertyNames()}")
            } catch (e: Exception) {
                logger.error("Failed to load config.properties", e)
            }
        }
    }

    /**
     * Gets a property value by its key.
     * @param key The property key.
     * @return The property value, or empty string if not found.
     */
    fun getProperty(key: String): String {
        return properties.getProperty(key) ?: ""
    }

    /**
     * Gets an integer property value by its key.
     * @param key The property key.
     * @param defaultValue The default value to return if the property is not found or is not a valid integer.
     * @return The property value as an Int, or the default value if not found or invalid.
     */
    fun getIntProperty(key: String, defaultValue: Int): Int {
        return getProperty(key)?.toIntOrNull() ?: defaultValue
    }

    /**
     * 获取指定环境的配置属性
     * @param key 属性键
     * @param environment 环境类型
     * @return 属性值，如果未找到则返回null
     */
    fun getEnvironmentProperty(key: String, environment: EnvironmentConfigUtil.Environment): String? {
        return EnvironmentConfigUtil.getProperty(key, environment)
    }

    /**
     * 获取当前环境
     */
    fun getCurrentEnvironment(): EnvironmentConfigUtil.Environment {
        return EnvironmentConfigUtil.getCurrentEnvironment()
    }

    /**
     * 重新加载配置
     */
    fun reloadConfig() {
        properties.clear()
        EnvironmentConfigUtil.clearCache()
        val envConfig = EnvironmentConfigUtil.getMergedConfig()
        properties.putAll(envConfig)
        logger.info("Configuration reloaded for environment: ${getCurrentEnvironment()}")
    }

    /**
     * 获取所有配置属性（用于调试）
     */
    fun getAllProperties(): Map<String, String> {
        return properties.stringPropertyNames().associateWith { properties.getProperty(it) }
    }
}
# 共享配置 - 所有环境通用的配置项
# Common configuration shared across all environments
# 如果配置需要区分内部开发或外部版本，请定义到internal.properties和public.properties中

# project basic info (shared)
project.group.id=com.teitui.toco
project.version=0.0.77

# locator (shared)
locator.file.extension=java
locator.source.path=src/main/java/
locator.class.annotation=com.vs.code.AutoGenerated
locator.method.annotation=com.vs.code.AutoGenerated
locator.default.annotation=AutoGenerated
locator.public.interface.annotation=com.vs.common.util.rpc.pub.PublicInterface
locator.controller.annotation=org.springframework.stereotype.Controller
locator.desktop.url.setproject=setProject?id={0}&name={1}

# API paths (shared)
url.path.login=/w
url.path.logout=/api/user-account/logout
url.path.userinfo=/api/user/info
url.path.search=/api/item/search
url.path.fim=/lm/api/code/completions
url.path.orgs=/mis/api/organization/query-organization-user-info-by-user
url.path.create.project=/mis/api/organization/create-project-to-organization
url.path.create.project.config=/workspace/api/boilerplate/server-project
url.path.create.module.config=/workspace/api/boilerplate/server-module
url.path.module.version=/api/module/authed/latest/version
url.path.create.project.version=/api/vs/version

package com.think1024.tocodesign.ideaplugin.services

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.toco.TextFilePatcher
import com.think1024.tocodesign.ideaplugin.utils.FileUtil
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * 代码合并服务，用于将变更的代码片段合并到原始代码中
 * 支持带有 "// ... existing code ..." 标记的代码变更格式
 */
@Service(Service.Level.PROJECT)
class CodeMergeService(private val project: Project) {
    private val logger = Logger.getInstance(CodeMergeService::class.java)

    /**
     * 应用变更结果
     */
    data class ApplyChangesResult(
        val isSuccess: <PERSON><PERSON><PERSON>,
        val message: String,
        val isNewFile: Boolean = false
    )

    /**
     * 将变更代码合并到原始代码中
     *
     * @param originCode 原始完整代码
     * @param changedCode 变更的代码片段，以 "// ... existing code ..." 分隔
     * @return 合并后的代码
     */
//    fun mergeCodeWithContext(originCode: String, changedCode: String): String {
//        // 如果原始代码或变更代码为空，直接返回原始代码
//        if (originCode.isBlank() || changedCode.isBlank()) {
//            return originCode
//        }
//
//        // 解析原始代码和变更代码的行
//        val originLines = originCode.lines()
//        val changedLines = changedCode.lines()
//
//        // 解析变更代码中的编辑块
//        val editBlocks = parseEditBlocks(changedLines)
//        logger.info("Found ${editBlocks.size} edit blocks")
//
//        // 应用编辑块到原始代码
//        return applyEdits(originLines, editBlocks)
//    }

    /**
     * 解析变更代码中的编辑块
     * @param changedLines 变更代码的行
     * @return 解析出的编辑块列表
     */
    private fun parseEditBlocks(changedLines: List<String>): List<EditBlock> {
        val blocks = mutableListOf<EditBlock>()
        var currentBlock = mutableListOf<String>()
        var isCollectingBlock = false  // 初始状态设为false，表示开始时在"existing code"区域

        // 分析每一行，提取编辑块
        for (line in changedLines) {
            val trimmedLine = line.trim()

            if (trimmedLine.startsWith("// ...") && trimmedLine.contains("existing code")) {
                // 遇到分隔符，切换状态
                if (isCollectingBlock && currentBlock.isNotEmpty()) {
                    // 如果正在收集块且块不为空，保存当前块
                    val trimmedBlock = trimLeadingAndTrailingEmptyLines(currentBlock.toList())
                    if (trimmedBlock.isNotEmpty()) {
                        blocks.add(EditBlock(trimmedBlock))
                    }
                    currentBlock.clear()
                }
                isCollectingBlock = !isCollectingBlock
            } else {
                if (!isCollectingBlock) {
                    isCollectingBlock = true
                }
                if (isCollectingBlock) {
                    // 只有在收集块状态下才添加行
                    currentBlock.add(line)
                }
            }
        }

        // 处理最后一个块
        if (isCollectingBlock && currentBlock.isNotEmpty()) {
            val trimmedBlock = trimLeadingAndTrailingEmptyLines(currentBlock.toList())
            if (trimmedBlock.isNotEmpty()) {
                blocks.add(EditBlock(trimmedBlock))
            }
        }

        // 处理特殊情况：如果列表为空但有代码，可能是没有分隔符的情况
        if (blocks.isEmpty() && changedLines.any { it.trim().isNotEmpty() && !it.trim().startsWith("// ...") }) {
            // 收集所有非分隔符行
            val allCode = changedLines.filter {
                !it.trim().startsWith("// ...") || !it.trim().contains("existing code")
            }
            if (allCode.isNotEmpty()) {
                blocks.add(EditBlock(allCode))
            }
        }

        // 记录日志，方便调试
        for (i in blocks.indices) {
            logger.info("块 #${i+1} 包含 ${blocks[i].lines.size} 行，首行: '${blocks[i].lines.firstOrNull() ?: "空"}'")
        }

        logger.info("共解析出 ${blocks.size} 个变更块")
        return blocks
    }

    /**
     * 去除列表开始和结尾的空行
     */
    private fun trimLeadingAndTrailingEmptyLines(lines: List<String>): List<String> {
        if (lines.isEmpty()) return emptyList()

        var startIndex = 0
        var endIndex = lines.size - 1

        // 寻找第一个非空行
        while (startIndex <= endIndex && lines[startIndex].trim().isEmpty()) {
            startIndex++
        }

        // 寻找最后一个非空行
        while (endIndex >= startIndex && lines[endIndex].trim().isEmpty()) {
            endIndex--
        }

        // 如果全是空行，返回空列表
        if (startIndex > endIndex) return emptyList()

        return lines.subList(startIndex, endIndex + 1)
    }

    /**
     * 应用编辑到原始代码
     * @param originLines 原始代码的行
     * @param editBlocks 编辑块列表
     * @return 合并后的代码
     */
    private fun applyEdits(originLines: List<String>, editBlocks: List<EditBlock>): String {
        if (editBlocks.isEmpty()) {
            return originLines.joinToString("\n")
        }

        val result = mutableListOf<String>()
        var originIndex = 0
        var lastFoundIndex = -1

        // 尝试应用每个编辑块
        for (block in editBlocks) {
            // 查找匹配点
            val matchResult = findBestMatchPosition(originLines, originIndex, block.lines)

            if (matchResult.matchIndex != -1) {
                // 添加匹配点之前的原始代码
                if (matchResult.matchIndex > originIndex) {
                    if (isMethodMatch(matchResult)) {
                        // 对于方法匹配，需要排除方法签名前的注释和注解
                        val codeBeforeMatch = originLines.subList(originIndex, matchResult.matchIndex)
                        val methodSignature = extractMethodSignature(block.lines)

                        if (methodSignature != null) {
                            // 找到原始代码中的方法签名位置
                            val originMethodIndex = findMethodLineIndex(originLines, methodSignature)

                            if (originMethodIndex >= 0) {
                                // 提取原始方法前的注解和注释
                                val annotationsAndComments = extractMethodAnnotationsAndComments(originLines, originMethodIndex)

                                if (annotationsAndComments.isNotEmpty()) {
                                    // 计算注解和注释的开始位置
                                    val commentStartIndex = originMethodIndex - annotationsAndComments.size

                                    // 只添加不包含注解和注释的部分
                                    if (commentStartIndex > originIndex) {
                                        result.addAll(originLines.subList(originIndex, commentStartIndex))
                                    }
                                } else {
                                    // 没有注释和注解，添加全部
                                    result.addAll(codeBeforeMatch)
                                }
                            } else {
                                // 找不到方法签名，保守处理，添加全部
                                result.addAll(codeBeforeMatch)
                            }
                        } else {
                            // 找不到方法签名，保守处理，添加全部
                            result.addAll(codeBeforeMatch)
                        }
                    } else {
                        // 非方法匹配，直接添加原始代码
                        result.addAll(originLines.subList(originIndex, matchResult.matchIndex))
                    }
                }

                // 检查是否是方法块，以及是否有重复注释需要处理
                val blockToApply = if (isMethodMatch(matchResult)) {
                    // 提取去除重复注释后的编辑块
                    extractNonDuplicateMethodBlockPart(block.lines, originLines, matchResult)
                } else {
                    // 非方法块，使用完整编辑块
                    block.lines
                }

                // 添加处理后的编辑块代码（可能已去除重复注释）
                result.addAll(blockToApply)

                // 更新原始代码索引位置
                originIndex = matchResult.endIndex
                lastFoundIndex = matchResult.matchIndex
            } else {
                // 如果这是一个新增方法（没有找到匹配），尝试添加到类的末尾
                if (isNewMethodBlock(block)) {
                    val classEndIndex = findClassEndIndex(originLines)
                    if (classEndIndex > 0) {
                        // 如果这是第一个没有匹配的块，先添加之前的原始代码
                        if (lastFoundIndex == -1) {
                            result.addAll(originLines.subList(originIndex, classEndIndex - 1))
                        }
                        // 添加一个空行和新方法
                        result.add("")
                        result.addAll(block.lines)
                        // 不更新originIndex，因为我们只是预先插入了这个方法
                        lastFoundIndex = classEndIndex - 1
                    }
                }
            }
        }

        // 添加剩余的原始代码
        if (originIndex < originLines.size) {
            result.addAll(originLines.subList(originIndex, originLines.size))
        }

        return result.joinToString("\n")
    }

    /**
     * 查找编辑块在原始代码中的最佳匹配位置
     * @return MatchResult 包含匹配位置和结束位置
     */
    private fun findBestMatchPosition(originLines: List<String>, startIndex: Int, blockLines: List<String>): MatchResult {
        if (blockLines.isEmpty()) return MatchResult(startIndex, startIndex)

        // 获取编辑块的有效内容行
        val contentLines = blockLines.filter { it.trim().isNotEmpty() }
        if (contentLines.isEmpty()) return MatchResult(startIndex, startIndex)

        // 尝试根据方法签名进行匹配
        val methodMatch = findMethodMatch(originLines, startIndex, blockLines)
        if (methodMatch.matchIndex >= 0) {
            return methodMatch
        }

        // 获取编辑块的第一个非空行作为锚点
        val anchorLine = contentLines.first().trim()

        // 根据锚点在原始代码中寻找匹配
        for (i in startIndex until originLines.size) {
            val originTrimmed = originLines[i].trim()
            if (originTrimmed.isEmpty()) continue

            if (isSignificantMatch(originTrimmed, anchorLine)) {
                // 尝试验证上下文是否匹配
                val contextValid = validateContext(originLines, i, blockLines)
                if (contextValid) {
                    // 计算结束位置
                    val endIndex = calculateEndIndex(originLines, i, blockLines)
                    return MatchResult(i, endIndex)
                }
            }
        }

        // 如果找不到匹配，返回无效结果
        return MatchResult(-1, -1)
    }

    /**
     * 提取方法签名
     */
    private fun extractMethodSignature(lines: List<String>): String? {
        // 匹配Java方法签名的正则表达式
        val methodPattern = """^\s*(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\(""".toRegex()

        for (line in lines) {
            val match = methodPattern.find(line)
            if (match != null) {
                // 提取不包含花括号的方法签名（去掉前后空白）
                val trimmedLine = line.trim()
                val bracketIndex = trimmedLine.indexOf('{')

                // 提取不包含右括号后面内容的方法签名
                val closeParenIndex = trimmedLine.indexOf(')', bracketIndex.takeIf { it > 0 } ?: 0)

                return if (closeParenIndex > 0) {
                    // 提取方法签名到右括号为止
                    trimmedLine.substring(0, closeParenIndex + 1).trim()
                } else if (bracketIndex > 0) {
                    // 如果包含花括号，截取花括号前的部分作为方法签名
                    trimmedLine.substring(0, bracketIndex).trim()
                } else {
                    // 如果没有花括号，直接使用整行
                    trimmedLine
                }
            }
        }

        return null
    }

    /**
     * 获取方法的注释内容
     */
    private fun extractMethodComment(lines: List<String>, methodLineIndex: Int): List<String> {
        if (methodLineIndex <= 0) return emptyList()

        val commentLines = mutableListOf<String>()
        var i = methodLineIndex - 1

        // 跳过方法前的空行
        while (i >= 0 && lines[i].trim().isEmpty()) {
            i--
        }

        // 收集注释行
        while (i >= 0) {
            val line = lines[i]
            if (line.trim().startsWith("/**") || line.trim().startsWith("*") || line.trim().startsWith("*/")) {
                commentLines.add(0, line) // 添加到列表头部
                i--
            } else {
                break
            }
        }

        return commentLines
    }

    /**
     * 获取方法签名所在的行Index
     */
    private fun findMethodLineIndex(lines: List<String>, methodSignature: String): Int {
        for (i in lines.indices) {
            val line = lines[i].trim()
            if (line == methodSignature || line.startsWith(methodSignature + " {") || line.startsWith(methodSignature + "{")) {
                return i
            }
        }
        return -1
    }

    /**
     * 根据方法签名寻找匹配
     */
    private fun findMethodMatch(originLines: List<String>, startIndex: Int, blockLines: List<String>): MatchResult {
        // 尝试从编辑块中提取方法签名
        val methodSignature = extractMethodSignature(blockLines) ?: return MatchResult(-1, -1)

        // 获取编辑块中方法签名的行索引
        val methodLineIndex = findMethodLineIndex(blockLines, methodSignature)

        if (methodLineIndex < 0) return MatchResult(-1, -1)

        // 提取编辑块中的方法注释
        val blockMethodComment = extractMethodComment(blockLines, methodLineIndex)

        // 在原始代码中寻找匹配的方法签名
        for (i in startIndex until originLines.size) {
            val originLine = originLines[i].trim()
            val originMethodSignature = extractMethodSignature(listOf(originLine))

            if (originMethodSignature != null && methodSignature == originMethodSignature) {
                // 找到匹配的方法签名，检查注释是否已存在
                val originMethodComment = extractMethodComment(originLines, i)

                // 找到方法结束位置
                val methodEndIndex = findMethodEndIndex(originLines, i)

                // 如果原来的方法已经有注释，且包含新的注释内容，则不重复添加注释
                if (originMethodComment.isNotEmpty() && containsSimilarComment(originMethodComment, blockMethodComment)) {
                    // 返��原始方法位置，避免重复添加注释
                    return MatchResult(i, methodEndIndex, true)
                }

                // 找不到相似注释，返回包含注释起始位置的匹配结果
                val commentStartIndex = i - originMethodComment.size
                return MatchResult(if (commentStartIndex > 0) commentStartIndex else i, methodEndIndex, true)
            }
        }

        return MatchResult(-1, -1)
    }

    /**
     * 检查两组注释是否相似（避免重复添加）
     */
    private fun containsSimilarComment(originComment: List<String>, newComment: List<String>): Boolean {
        if (newComment.isEmpty()) return true // 如果没有新注释，不需要添加
        if (originComment.isEmpty()) return false // 如果原来没有注释，需要添加新注释

        // 提取注释中的实际内容（去除*和空格）进行比较
        val originContent = originComment
            .joinToString(" ") { it.replace("/**", "").replace("*/", "").replace("*", "").trim() }
            .trim()

        val newContent = newComment
            .joinToString(" ") { it.replace("/**", "").replace("*/", "").replace("*", "").trim() }
            .trim()

        // 如果原注释已包含新注释的内容，则认为相似
        return originContent.contains(newContent) || newContent.contains(originContent)
    }

    /**
     * 查找方法结束位置
     */
    private fun findMethodEndIndex(lines: List<String>, startIndex: Int): Int {
        var braceCount = 0
        var methodStarted = false

        for (i in startIndex until lines.size) {
            val line = lines[i]

            // 计数花括号
            for (char in line) {
                if (char == '{') {
                    methodStarted = true
                    braceCount++
                } else if (char == '}') {
                    braceCount--
                }
            }

            // 当方法已开始且花括号平衡时，找到了方法结束
            if (methodStarted && braceCount == 0) {
                return i + 1  // 返回方法后的下一行索引
            }
        }

        return lines.size  // 如果没找到方法结束，返回代码末尾
    }

    /**
     * 查找类结束位置
     */
    private fun findClassEndIndex(lines: List<String>): Int {
        // 寻找最后一个右花括号
        for (i in lines.size - 1 downTo 0) {
            if (lines[i].trim() == "}") {
                return i
            }
        }
        return lines.size
    }

    /**
     * 判断一个块是否是新增的方法
     */
    private fun isNewMethodBlock(block: EditBlock): Boolean {
        val nonEmptyLines = block.lines.filter { it.trim().isNotEmpty() }
        if (nonEmptyLines.isEmpty()) return false

        // 检查是否包含方法签名并且有方法体
        val methodPattern = """^\s*(?:public|private|protected)?\s*(?:static)?\s*\w+\s+\w+\s*\(""".toRegex()
        val hasMethodSignature = nonEmptyLines.any { methodPattern.matches(it) }

        // 检查是否有方法注释
        val hasJavadoc = nonEmptyLines.any { it.trim().startsWith("/**") }

        // 检查是否有花括号（方法体）
        val hasMethodBody = nonEmptyLines.any { it.contains("{") }

        return (hasMethodSignature && hasMethodBody) || hasJavadoc
    }

    /**
     * 计算方法块结束位置
     */
    private fun calculateEndIndex(originLines: List<String>, startIndex: Int, blockLines: List<String>): Int {
        // 获取编辑块的最后一个实质性行
        val lastSubstantialLine = blockLines.lastOrNull { it.trim().isNotEmpty() }?.trim() ?: return startIndex + 1

        // 如果是方法签名，需要找到整个方法的结束位置
        if (isMethodSignature(blockLines.firstOrNull()?.trim() ?: "")) {
            return findMethodEndIndex(originLines, startIndex)
        }

        // 否则，尝试在原始代码中找到与最后一行匹配的位置
        for (i in startIndex until originLines.size) {
            val originLine = originLines[i].trim()
            if (originLine.isNotEmpty() && isSignificantMatch(originLine, lastSubstantialLine)) {
                return i + 1  // 返回匹配行的下一行
            }
        }

        // 如果找不到匹配，估算一个合理的值
        return startIndex + blockLines.count { it.trim().isNotEmpty() }
    }

    /**
     * 判断一行是否是方法签名
     */
    private fun isMethodSignature(line: String): Boolean {
        val methodPattern = """^\s*(?:public|private|protected)?\s*(?:static)?\s*\w+\s+\w+\s*\(""".toRegex()
        return methodPattern.matches(line)
    }

    /**
     * 判断两行代码是否匹配（忽略空白）
     */
    private fun isSignificantMatch(line1: String, line2: String): Boolean {
        // 移除注释后比较
        val code1 = removeComments(line1).trim()
        val code2 = removeComments(line2).trim()

        // 完全相等的情况
        if (code1 == code2) return true

        // 方法签名匹配
        if (code1.contains("(") && code2.contains("(")) {
            val methodName1 = extractMethodName(code1)
            val methodName2 = extractMethodName(code2)
            if (methodName1.isNotEmpty() && methodName1 == methodName2) {
                return true
            }
        }

        // 检查变量声明或赋值语句
        if ((code1.contains("=") || code2.contains("=")) &&
            extractVariableName(code1) == extractVariableName(code2)) {
            return true
        }

        return false
    }

    /**
     * 提取变量���
     */
    private fun extractVariableName(line: String): String {
        val variablePattern = """^\s*(?:final\s+)?(?:\w+\s+)+(\w+)\s*=""".toRegex()
        val match = variablePattern.find(line)
        return match?.groupValues?.get(1) ?: ""
    }

    /**
     * 验证上下文是否匹配
     */
    private fun validateContext(originLines: List<String>, position: Int, blockLines: List<String>): Boolean {
        // 获取编辑块中的几个关键行
        val keyLines = blockLines.filter { it.trim().isNotEmpty() }
            .take(3)
            .map { it.trim() }

        if (keyLines.isEmpty()) return true

        var matches = 0

        // 查找关键行是否在原始代码中匹配
        for (i in 0 until minOf(10, originLines.size - position)) {
            val originLine = originLines[position + i].trim()
            if (originLine.isEmpty()) continue

            if (keyLines.any { isSignificantMatch(it, originLine) }) {
                matches++
                if (matches >= 1) return true  // 至少有一行匹配即可
            }
        }

        return false
    }

    /**
     * 移除代码行中的注释
     */
    private fun removeComments(line: String): String {
        val commentStart = line.indexOf("//")
        return if (commentStart >= 0) {
            line.substring(0, commentStart)
        } else {
            line
        }
    }

    /**
     * 从代码行中提取方法名
     */
    private fun extractMethodName(line: String): String {
        val methodPattern = "\\s*(\\w+)\\s*\\(".toRegex()
        val match = methodPattern.find(line)
        return match?.groupValues?.get(1) ?: ""
    }

    /**
     * 将更改应用到文件
     *
     * @param filePath 目标文件路径
     * @param changedCode 包含变更的代码片段
     * @return 应用变更的结果，包含成功状态和消息
     */
    fun applyChangesToFile(filePath: String, changedCode: String): ApplyChangesResult {
        // 获取原始文件内容
        val originalCode = CodeFileService.getInstance(project).getFileContent(filePath)

        // 合并代码（如果原始代码不存在，则使用空字符串）
        val (mergedCode, message) = TextFilePatcher.patchText(originalCode?:"", changedCode)
        if (mergedCode == null) {
            return ApplyChangesResult(false, message ?: "代码合并失败")
        }

        // FIXME: 测试日志
        logger.info("editFile 合并代码结果: $mergedCode\n原始代码: $originalCode\n变更代码: $changedCode")

        // 检查目标文件是否存在
        val isNewFile = !FileUtil.fileExists(filePath, project)

        // 代码合并成功，现在检查目标文件是否存在，如果不存在则创建该文件
        if (isNewFile) {
            logger.info("目标文件不存在，尝试创建: $filePath")
            if (FileUtil.createFile(filePath, project) == null) {
                return ApplyChangesResult(false, "无法创建文件: $filePath", isNewFile)
            }
            // 在FileOriginalVersionService里记录是新建文件
//            FileOriginalVersionService.getInstance(project).markAsNewFile(filePath)
        } else {
            logger.info("目标文件已存在: $filePath")
        }

        // 将合并后的内容写回文件
        try {
            // 确保在EDT线程上执行文件修改操作
            val latch = CountDownLatch(1)
            var success = false
            var errorMessage = ""

            ApplicationManager.getApplication().invokeLater {
                try {
                    ApplicationManager.getApplication().runWriteAction {
                        val virtualFile = FileUtil.getVirtualFile(filePath, project)
                        if (virtualFile == null) {
                            errorMessage = "无法获取虚拟文件: $filePath"
                            return@runWriteAction
                        }
                        virtualFile.setBinaryContent(mergedCode.toByteArray())
                        success = true
                    }
                } catch (e: Exception) {
                    logger.warn("Failed to write file in EDT: $filePath", e)
                    errorMessage = "写入文件失败: ${e.message}"
                } finally {
                    latch.countDown()
                }
            }

            // 等待EDT线程上的操作完成
            val completed = latch.await(5, TimeUnit.SECONDS)

            return when {
                !completed -> ApplyChangesResult(false, "文件写入操作超时", isNewFile)
                success -> ApplyChangesResult(true, message ?: "文件更新成功", isNewFile)
                else -> ApplyChangesResult(false, errorMessage.ifEmpty { "文件写入失败" }, isNewFile)
            }
        } catch (e: Exception) {
            logger.warn("Failed to write file: $filePath", e)
            return ApplyChangesResult(false, "文件写入异常: ${e.message}", isNewFile)
        }
    }

    /**
     * 检查匹配结果是否为方法匹配
     */
    private fun isMethodMatch(matchResult: MatchResult): Boolean {
        return matchResult.isMethodMatch
    }

    /**
     * 提取不包含重复注释的方法块部分
     * 如果原始代码中已经有相似注释，则仅保留方法体部分
     */
    private fun extractNonDuplicateMethodBlockPart(blockLines: List<String>,
                                                 originLines: List<String>,
                                                 matchResult: MatchResult): List<String> {
        // 如果不是方法匹配或匹配结果无效，直接返回原始块
        if (!matchResult.isMethodMatch || matchResult.matchIndex < 0) {
            return blockLines
        }

        // 尝试找出方法签名的位置
        val methodSignature = extractMethodSignature(blockLines)
        if (methodSignature == null) {
            return blockLines
        }

        val methodIndex = findMethodLineIndex(blockLines, methodSignature)

        if (methodIndex < 0) {
            return blockLines
        }

        // 提取编辑块中的所有注解行和注释行
        val blockAnnotationAndComments = extractMethodAnnotationsAndComments(blockLines, methodIndex)

        // 如果块中没有注释和注解，直接返回原始块
        if (blockAnnotationAndComments.isEmpty()) {
            return blockLines
        }

        // 提取原始代码中的方法签名位置
        val originMethodIndex = findMethodLineIndex(originLines, methodSignature)

        if (originMethodIndex < 0) {
            return blockLines
        }

        // 提取原始代码中的所有注解和注释
        val originAnnotationAndComments = extractMethodAnnotationsAndComments(originLines, originMethodIndex)

        // 如果原始代码中已有注释和注解，需要合并而非简单替换
        if (originAnnotationAndComments.isNotEmpty()) {
            // 合并后的编辑块应该包含：
            // 1. 优先使用新的注释
            // 2. 合并注解，但忽略新代码中的@PublicInterface和@AutoGenerated

            // 构建合并后的代码块
            val mergedBlock = mutableListOf<String>()

            // 提取原始和变更代码中的注释
            val originComments = originAnnotationAndComments.filter {
                it.trim().startsWith("/**") || it.trim().startsWith("*") || it.trim().startsWith("*/") || it.trim().startsWith("//")
            }
            val blockComments = blockAnnotationAndComments.filter {
                it.trim().startsWith("/**") || it.trim().startsWith("*") || it.trim().startsWith("*/") || it.trim().startsWith("//")
            }

            // 始终优先使用新的注释，只在新代码没有注释时才使用原注释
            if (blockComments.isNotEmpty()) {
                mergedBlock.addAll(blockComments)
            } else if (originComments.isNotEmpty()) {
                mergedBlock.addAll(originComments)
            }

            // 提取原始和新代码中的注解
            val blockAnnotations = blockAnnotationAndComments.filter { it.trim().startsWith("@") }
            val originAnnotations = originAnnotationAndComments.filter { it.trim().startsWith("@") }

            // 合并所有唯一的注解
            val annotationMap = mutableMapOf<String, String>()

            // 先处理原始注解
            for (annotation in originAnnotations) {
                val trimmedAnnotation = annotation.trim()
                val annotationName = trimmedAnnotation.substring(1).takeWhile { !it.isWhitespace() && it != '(' }
                annotationMap[annotationName] = annotation
            }

            // 再处理新注解，忽略@PublicInterface和@AutoGenerated
            for (annotation in blockAnnotations) {
                val trimmedAnnotation = annotation.trim()
                val annotationName = trimmedAnnotation.substring(1).takeWhile { !it.isWhitespace() && it != '(' }

                // 忽略特定的注解
                if (annotationName != "PublicInterface" && annotationName != "AutoGenerated") {
                    annotationMap[annotationName] = annotation
                }
            }

            // 将所有收集到的唯一注解添加到结果中
            val mergedAnnotations = annotationMap.values.toList()

            // 添加去重后的注解
            mergedBlock.addAll(mergedAnnotations)

            // 添加方法体部分
            mergedBlock.addAll(blockLines.subList(methodIndex, blockLines.size))

            return mergedBlock
        }

        // 如果原始代码中没有注释和注解，保留块中的全部内容，但过滤掉特定注解
        if (blockAnnotationAndComments.any {
            val trimmed = it.trim()
            trimmed.startsWith("@PublicInterface") || trimmed.startsWith("@AutoGenerated")
        }) {
            // 构建过滤后的块
            val filteredBlock = mutableListOf<String>()

            // 分析每一行，过滤掉特定注解
            var i = 0
            while (i < methodIndex) {
                val line = blockLines[i].trim()
                if (!(line.startsWith("@PublicInterface") || line.startsWith("@AutoGenerated"))) {
                    filteredBlock.add(blockLines[i])
                }
                i++
            }

            // 添加方法体部分
            filteredBlock.addAll(blockLines.subList(methodIndex, blockLines.size))

            return filteredBlock
        }

        return blockLines
    }

    /**
     * 提取方法的所有注解和注释（包括Javadoc和单行注释）
     */
    private fun extractMethodAnnotationsAndComments(lines: List<String>, methodLineIndex: Int): List<String> {
        if (methodLineIndex <= 0) return emptyList()

        val result = mutableListOf<String>()
        var i = methodLineIndex - 1

        // 跳过方法前的空行
        while (i >= 0 && lines[i].trim().isEmpty()) {
            i--
        }

        // 收集注解和注释行
        while (i >= 0) {
            val line = lines[i].trim()
            if (line.startsWith("/**") || line.startsWith("*") || line.startsWith("*/") ||
                line.startsWith("//") || line.startsWith("@")) {
                result.add(0, lines[i]) // 添加到列表头部
                i--
            } else {
                break
            }
        }

        return result
    }

    /**
     * 表示一个编辑块
     */
    data class EditBlock(val lines: List<String>)

    /**
     * 表示匹配结果
     */
    data class MatchResult(val matchIndex: Int, val endIndex: Int, val isMethodMatch: Boolean = false)

    companion object {
        /**
         * 获取服务实例
         */
        fun getInstance(project: Project): CodeMergeService = project.service()
    }
}

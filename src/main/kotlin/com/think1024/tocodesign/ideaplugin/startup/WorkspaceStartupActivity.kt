package com.think1024.tocodesign.ideaplugin.startup
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.ProgressManager
import com.intellij.openapi.progress.Task
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.actions.hookCloseActions
import com.think1024.tocodesign.ideaplugin.actions.hookSplitActions
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.toco.BoilerplateCmd
import com.think1024.tocodesign.ideaplugin.toco.Fossil
import com.think1024.tocodesign.ideaplugin.toco.FossilResult
import com.think1024.tocodesign.ideaplugin.toco.Item
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
//import com.think1024.tocodesign.ideaplugin.toco.BoilerplateCmd
import com.think1024.tocodesign.ideaplugin.utils.FileUtil
import com.think1024.tocodesign.ideaplugin.utils.ProjectConfigUtil
import com.think1024.tocodesign.ideaplugin.webview.WebviewPackage

class WorkspaceStartupActivity: TocoProjectActivity() {
    override fun runActivity(project: Project) {
        if (ProjectPluginSettings.getInstance(project).projectId == null) {
            return
        }
        hookCloseActions()
        hookSplitActions()
        // 启动同步载入离线包到内存
        WebviewPackage
        ProgressManager.getInstance().run(object : Task.Backgroundable(project, getI18nString("fossil.project.init"), true) {
            override fun run(indicator: ProgressIndicator) {
                indicator.isIndeterminate = true

                fun stop() {
                    indicator.isIndeterminate = false
                    indicator.fraction = 1.0
                }

                val projectWorkFolder = project.basePath ?: return
                val projectInfo = ProjectConfigUtil.getProjectInfo(projectWorkFolder) ?: return
                val name = projectInfo["name"]
                val id = projectInfo["id"]
                if (name == null || id == null) {
                    stop()
                    return
                }
                Fossil.fixFossilAdmin(projectWorkFolder)
                // 如果之前操作中断出现fossil在merge状态的话，revert
                Fossil.abortMergeIfNeeded(projectWorkFolder)
                val item = Item(id, name)
                val projectFile = FileUtil.getFile(Fossil.getProjectFile(name))
                if (projectFile == null) {
                    val result = Fossil.createProject(item)
                    if (!result.success) {
                        stop()
                        return
                    }
                }
                Fossil.initProject(item, projectWorkFolder) { _, _ -> FossilResult(true) }
                indicator.text = getI18nString("fossil.project.init.finished")
                stop()
            }
        })
        // 插件启动后启动本地server
//        BoilerplateCmd.startServer()
        // 生成本地HTTPS证书，用于前端连通性测试
        BoilerplateCmd.mkcert(project)
    }
}
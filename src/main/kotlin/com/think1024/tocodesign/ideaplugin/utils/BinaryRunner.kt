package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.fileTypes.FileTypeRegistry
import com.think1024.tocodesign.ideaplugin.toco.BoilerplateCmd
import net.jpountz.xxhash.XXHashFactory
import java.io.*
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.StandardCopyOption
import java.util.*
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

object BinaryRunner {
    private val paths: MutableMap<String, String> = mutableMapOf()

    data class Result(val output: String, val exitCode: Int, val error: String?)

    fun isBinaryFile(path: String): Boolean? {
        // 1. 首先使用 IntelliJ 平台的文件类型判断
        val type = FileTypeRegistry.getInstance().getFileTypeByFileName(path)
        // type是UnknownFileType的时候isBinary是true，需要排除
        if (type.isBinary && type.name != "UNKNOWN") {
            return true
        }

        val file = FileUtil.getFile(path) ?: return null
        
        // 2. 检查文件扩展名，常见的文本文件扩展名直接返回 false
        val textExtensions = setOf(
            "txt", "java", "kt", "kts", "xml", "json", "yaml", "yml", "properties", 
            "md", "html", "htm", "css", "js", "ts", "jsx", "tsx", "gradle", "sh", 
            "bat", "cmd", "sql", "c", "cpp", "h", "hpp", "py", "rb", "php", "go"
        )
        
        if (file.extension.lowercase() in textExtensions) {
            return false
        }
        
        // 3. 对于其他文件，使用改进的二进制检测算法
        try {
            val content = file.readBytes()
            val sampleSize = minOf(content.size, 4096)
            
            if (sampleSize == 0) return false // 空文件视为文本文件
            
            // 统计二进制字符的数量
            var binaryCharCount = 0
            
            for (i in 0 until sampleSize) {
                val byte = content[i].toInt() and 0xFF
                // 排除常见的文本文件控制字符：CR(13), LF(10), TAB(9)
                if (byte == 0 || (byte < 9 && byte != 0) || (byte > 13 && byte < 32)) {
                    binaryCharCount++
                }
            }
            
            // 如果二进制字符占比超过 1%，判定为二进制文件
            return (binaryCharCount.toFloat() / sampleSize) > 0.01
        } catch (e: Exception) {
            // 如果读取文件内容失败，保守地返回 true（认为是二进制文件）
            return true
        }
    }

    fun getBinaryPath(name: String): Path {
        val ret = paths[name]
        if (!ret.isNullOrEmpty()) {
            return Path.of(ret)
        }

        // 获取插件数据目录
        val binDir = Path.of(PathUtil.getPluginDataDir().toString(), "bin")
        Files.createDirectories(binDir)

        val platform = getPlatform()
        val isWin = platform == "win32"
        val binaryName = if (isWin) "$name.exe" else name
        val binaryPath = binDir.resolve(binaryName)
        val resourcePath = "/$platform/$binaryName"
        val fileWrote = ResourceLoader.extractResource(resourcePath, binaryPath.toString())
        if (fileWrote && !isWin) {
            binaryPath.toFile().setExecutable(true)
        }
        paths[name] = binaryPath.toString()
        return binaryPath
    }

    fun getPlatform(os: String = System.getProperty("os.name").lowercase(Locale.getDefault())): String = when {
        os.contains("linux") -> "linux"
        os.contains("windows") -> "win32"
        os.contains("mac") -> "darwin"
        else -> throw UnsupportedOperationException("Unsupported OS: $os")
    }

    /**
     * 同步执行指定路径的二进制命令
     */
    fun runSync(
        binaryPath: String,
        vararg args: String,
        cwd: String = "",
    ): Result {
        val file = FileUtil.getFile(binaryPath)
        if (file == null || !file.canExecute()) {
            return Result("", -1, "File not found or not executable: $binaryPath")
        }

        return try {
            var processBuilder = ProcessBuilder(binaryPath, *args).redirectErrorStream(false)
            val dir = FileUtil.getFile(cwd)
            if (dir != null) {
                processBuilder = processBuilder.directory(dir)
            }
            val process = processBuilder.start()
            
            // 创建线程池来并行读取输出和错误流
            val executor = Executors.newFixedThreadPool(2)
            
            // 使用 Future 来获取输出结果
            val outputFuture = executor.submit<String> {
                process.inputStream.bufferedReader().use { it.readText() }
            }
            
            val errorFuture = executor.submit<String> {
                process.errorStream.bufferedReader().use { it.readText() }
            }
            
            // 等待进程完成
            val exitCode = process.waitFor()
            
            // 获取输出和错误流的内容
            val output = outputFuture.get()
            val error = errorFuture.get()
            
            // 关闭线程池
            executor.shutdown()
            executor.awaitTermination(1, TimeUnit.SECONDS)
            
            Result(output.trim(), exitCode, error)
        } catch (e: Exception) {
            Result("", -1, e.message)
        }
    }

    /**
     * 异步执行二进制命令
     */
    fun runAsync(
        binaryPath: String,
        vararg args: String,
        callback: (Result) -> Unit,
        cwd: String = "",
    ) {
        ApplicationManager.getApplication().executeOnPooledThread {
            val result = runSync(binaryPath, *args, cwd = cwd)
            callback(result)
        }
    }

    /**
     * 持续运行，没有超时时间
     */
    fun runCommand(
        binaryPath: String,
        vararg args: String,
        cwd: String = "",
        onOutput: ((String) -> Unit)? = null,
        onError: ((String) -> Unit)? = null,
        onExit: ((Int) -> Unit)? = null,
    ): Process? {
        var process: Process? = null
        try {
            // 启动进程
            var processBuilder = ProcessBuilder(binaryPath, *args).redirectErrorStream(false)
            val dir = FileUtil.getFile(cwd)
            if (dir != null) {
                processBuilder = processBuilder.directory(dir)
            }
            process = processBuilder.start()

            // 创建线程池异步读取输出
            val executor = Executors.newFixedThreadPool(2)

            // 异步读取标准输出
            executor.submit {
                BufferedReader(InputStreamReader(process.inputStream)).use { reader ->
                    reader.lineSequence().forEach { line ->
                        onOutput?.let { it(line) }
                    }
                }
            }

            // 异步读取标准错误
            executor.submit {
                BufferedReader(InputStreamReader(process.errorStream)).use { reader ->
                    reader.lineSequence().forEach { line ->
                        onError?.let { it(line) }
                    }
                }
            }

            // 监控进程退出
            executor.submit {
                try {
                    val exitCode = process.waitFor()
                    onExit?.let { it(exitCode) }
                } catch (e: InterruptedException) {
                    onError?.let { it("Process interrupted: ${e.message}") }
                    process.destroy()
                } finally {
                    executor.shutdown()
                    try {
                        executor.awaitTermination(1, TimeUnit.SECONDS)
                    } catch (e: InterruptedException) {
                        // Ignore
                    }
                }
            }
        } catch (e: IOException) {
            onError?.let { it("Failed to start process: ${e.message}") }
        }
        return process
    }
}

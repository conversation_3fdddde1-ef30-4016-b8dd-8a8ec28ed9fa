package com.think1024.tocodesign.ideaplugin.actions

import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.ide.CopyPasteManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.think1024.tocodesign.ideaplugin.services.NotificationService
import com.think1024.tocodesign.ideaplugin.toco.TocoVirtualFile
import com.think1024.tocodesign.ideaplugin.utils.ConfigUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import com.think1024.tocodesign.ideaplugin.utils.getPluginSettings
import java.awt.datatransfer.StringSelection
import java.net.URLEncoder

/**
 * 复制文件链接的 Action
 * 
 * 此 Action 会复制当前打开文件的链接到剪贴板，仅支持 Toco 虚拟文件
 */
class CopyFileUrlAction : AnAction() {
    private val logger = Logger.getInstance(CopyFileUrlAction::class.java)

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        val file = getSelectedFile(project) ?: return
        
        // 只处理 Toco 虚拟文件
        if (file !is TocoVirtualFile) return
        
        // 获取 Toco 虚拟文件的 webUrl
        val url = file.pendingUrl ?: file.webUrl
        
        // 复制到剪贴板
        CopyPasteManager.getInstance().setContents(StringSelection(url))
        
        // 显示通知
        NotificationService.instance.notifyInfo(
            getI18nString("copy.file.url.success"),
            getI18nString("copy.file.url.success.description", url),
            project,
        )
    }
    
    /**
     * 获取当前选中的文件
     */
    private fun getSelectedFile(project: Project): VirtualFile? {
        val fileEditorManager = FileEditorManager.getInstance(project)
        return fileEditorManager.selectedFiles.firstOrNull()
    }
    
    /**
     * 更新 Action 状态
     * 只有当选中的文件是 Toco 虚拟文件时才启用此 Action
     */
    override fun update(e: AnActionEvent) {
        val project = e.project
        if (project == null) {
            e.presentation.isEnabledAndVisible = false
            return
        }
        
        val file = getSelectedFile(project)
        
        // 只有当文件是 TocoVirtualFile 类型时才启用和显示此 Action
        val isTocoFile = file is TocoVirtualFile
        e.presentation.isEnabledAndVisible = isTocoFile
    }
    
    /**
     * 指定 Action 更新线程
     */
    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.EDT
    }
}
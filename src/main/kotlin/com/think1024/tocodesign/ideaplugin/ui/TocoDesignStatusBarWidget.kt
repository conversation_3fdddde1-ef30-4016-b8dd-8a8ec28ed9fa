package com.think1024.tocodesign.ideaplugin.ui
import com.intellij.ide.BrowserUtil
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.compiler.*
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileTypes.FileTypeManager
import com.intellij.openapi.module.ModuleUtil
import com.intellij.openapi.options.ShowSettingsUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.IconLoader
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.wm.StatusBar
import com.intellij.openapi.wm.StatusBarWidget
import com.intellij.openapi.wm.WindowManager
import com.intellij.psi.PsiDocumentManager
import com.intellij.ui.JBColor
import com.intellij.util.Consumer
import com.intellij.util.ui.JBUI
import com.think1024.tocodesign.ideaplugin.services.QuickFixCompiler
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.settings.CombinedPluginSettingsConfigurable
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.toco.AutoMerge
import com.think1024.tocodesign.ideaplugin.toco.AutoMergeOptions
import com.think1024.tocodesign.ideaplugin.toco.Fossil
import com.think1024.tocodesign.ideaplugin.utils.ChangedFilesUtil
import com.think1024.tocodesign.ideaplugin.utils.ConfigUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import java.awt.Font
import java.awt.Point
import java.awt.event.MouseEvent
import java.net.URLEncoder
import java.nio.file.Paths
import java.text.MessageFormat
import java.time.LocalTime
import javax.swing.Icon
import javax.swing.JLabel
import javax.swing.JMenuItem
import javax.swing.JPopupMenu
import kotlin.io.path.extension

/**
 * Status bar widget for TocoDesign plugin.
 * This widget displays the current status of the locator service and provides a popup menu with additional options.
 * It implements StatusBarWidget, StatusBarWidget.IconPresentation interfaces.
 *
 * @param project The current project instance.
 */
class TocoDesignStatusBarWidget(private val project: Project) : StatusBarWidget, StatusBarWidget.IconPresentation {

    companion object {
        /**
         * Unique identifier for the TocoDesign status bar widget.
         */
        const val ID = "TocoDesignStatusBarWidget"

        /**
         * Logger instance for this class.
         */
        private val LOG = Logger.getInstance(TocoDesignStatusBarWidget::class.java)

        /**
         * Icon displayed when the locator is inactive (offline or unpaired).
         */
        private val ICON_INACTIVE = IconLoader.getIcon("/icons/toco.svg", TocoDesignStatusBarWidget::class.java)

        /**
         * Icon displayed when the locator is active (online and paired).
         */
        private val ICON_ACTIVE = IconLoader.getIcon("/icons/toco_color.svg", TocoDesignStatusBarWidget::class.java)
    }

    /**
     * Reference to the status bar where this widget is installed.
     */
    private var statusBar: StatusBar? = null

    /**
     * Settings specific to the current project.
     */
    private val projectSettings = ProjectPluginSettings.getInstance(project)

    /**
     * Application-wide settings for the plugin.
     */
    private val applicationSettings = ApplicationPluginSettings.getInstance()

    /**
     * Reference to the currently displayed popup menu, if any.
     */
    private var currentPopup: JPopupMenu? = null

    init {
        // Check if project ID is not null or empty before proceeding
        if (projectSettings.projectId.isNullOrEmpty()) {
            LOG.warn("Project ID is null or empty. TocoDesignStatusBarWidget not initialized.")
        }
    }

    // StatusBarWidget interface methods
    override fun ID(): String = ID
    override fun getPresentation(): StatusBarWidget.WidgetPresentation = this

    /**
     * Called when the widget is installed on the status bar.
     * @param statusBar The status bar instance.
     */
    override fun install(statusBar: StatusBar) {
        this.statusBar = statusBar
    }

    /**
     * Called when the widget is being disposed.
     * Removes this widget as a listener from the locator service.
     */
    override fun dispose() {
        statusBar = null
    }

    // StatusBarWidget.IconPresentation interface methods
    /**
     * Returns the icon to be displayed on the status bar.
     * The icon changes based on the locator's online and paired status.
     */
    override fun getIcon(): Icon = ICON_INACTIVE

    /**
     * Returns a consumer that handles mouse click events on the widget.
     */
    override fun getClickConsumer(): Consumer<MouseEvent> = Consumer { event ->
        val popup = createPopupMenu()
        showPopupMenu(popup, event)
    }

    /**
     * Returns the tooltip text for the widget.
     */
    override fun getTooltipText(): String = getI18nString("statusbar.widget.tooltip")

    /**
     * Creates the popup menu for the status bar widget.
     * @return A JPopupMenu instance with all menu items.
     */
    private fun createPopupMenu(): JPopupMenu {
        val popup = JPopupMenu()
        popup.border = JBUI.Borders.empty(10)

        // Add title
        val titleLabel = JLabel(getI18nString("statusbar.menu.title"))
        titleLabel.font = JBUI.Fonts.label().deriveFont(Font.BOLD)
        titleLabel.border = JBUI.Borders.emptyBottom(10)
        popup.add(titleLabel)

        popup.addSeparator()

        // Project location
        val projectLocationItem = JMenuItem(getI18nString("statusbar.menu.open.setting"))
        projectLocationItem.addActionListener { openPluginSettingsToApplicationTab() }
        popup.add(projectLocationItem)

        popup.addSeparator()

        // Login status
        popup.add(createLoginMenuItem())

        // Logout
        popup.add(createLogoutMenuItem())

        // Locator status
//        popup.add(createLocatorStatusMenuItem())

        // Add Fix Compile menu item
//        popup.add(createFixCompileMenuItem())

        // getFileList
//        popup.add(getFileList())

        // Apply custom UI settings to all menu items
        for (i in 0 until popup.componentCount) {
            val component = popup.getComponent(i)
            if (component is JMenuItem) {
                component.border = JBUI.Borders.empty(5, 10)
            }
        }

        return popup
    }

    private fun getFileComplierInfo(): JMenuItem {
        return JMenuItem("获取指定文件编译错误").apply {
            foreground = JBColor.YELLOW
            addActionListener { testApplyQuickFixAndCompile() }
        }
    }

    private fun testApplyQuickFixAndCompile() {
        val editor = FileEditorManager.getInstance(project).selectedTextEditor
        if (editor != null) {
            val psiFile = PsiDocumentManager.getInstance(project).getPsiFile(editor.document)
            if (psiFile != null) {
                val fileList: List<String> = listOf("file:///Users/<USER>/TocoTest/toco_test_hc_one/common/src/main/java/com/toco_test_hc_one/tocoTestHcOne/common/rocketmq/RocketMQServiceImpl.java")
                val test = QuickFixCompiler(project)
//                test.applyQuickFixAndCompile()
                test.applyQuickFixToFiles(fileList);
            }
        }
    }

    /**
     * Creates the login menu item based on the current login status.
     * @return A JMenuItem instance representing the login status.
     */
    private fun createLoginMenuItem(): JMenuItem {
        return if (applicationSettings.loggedIn) {
            JMenuItem(getI18nString("statusbar.menu.login.status.logged_in", applicationSettings.username)).apply {
                isEnabled = false
            }
        } else {
            JMenuItem(getI18nString("statusbar.menu.login.status.not_logged_in")).apply {
                foreground = JBColor.BLUE
                addActionListener { LoginHandler.getInstance(project).handleLogin() }
            }
        }
    }

    private fun createLogoutMenuItem(): JMenuItem {
        return if (applicationSettings.loggedIn) {
            JMenuItem(getI18nString("account.logout")).apply {
                isEnabled = true
                addActionListener { LoginHandler.getInstance(project).handleLogout() }
            }
        } else {
            JMenuItem(getI18nString("account.logout")).apply {
                isEnabled = false
            }
        }
    }

    /**
     * Opens the plugin settings dialog and selects the application tab.
     */
    private fun openPluginSettingsToApplicationTab() {

        val code = Fossil.format("import com.netease.plugin.*;\n" +
                "import com.netease.plugin.NewBaseDemo;\n" +
                "import com.netease.plugin.NewGenerate;\n" +
                "import com.vs.code.AutoGenerated;\n" +
                "import jdk.jfr.BooleanFlag;\n" +
                "import jdk.jfr.DataAmount;\n" +
                "import jdk.jfr.Enabled;\n" +
                "\n" +
                "\n" +
                "import java.io.Serializable;\n" +
                "import java.util.concurrent.TimeoutException;\n" +
                "\n" +
                "@Deprecated\n" +
                "@BooleanFlag\n" +
                "@AutoGenerated(uuid = \"1234\", locked = false)\n" +
                "@Enabled\n" +
                "@DataAmount\n" +
                "public abstract class Demo<T, S> extends NewBaseDemo<T, S> implements Serializable, Runnable {\n" +
                "    Custom custom = new Custom();\n" +
                "    @AutoGenerated\n" +
                "    NewGenerate newTest = new NewGenerate();\n" +
                "    @AutoGenerated(uuid = \"12345\", locked = true)\n" +
                "    NewGenerate test3 = new NewGenerate();\n" +
                "    @AutoGenerated(uuid = \"123444444\")\n" +
                "    NewGenerate anotherNewTest = new NewGenerate();\n" +
                "\n" +
                "    public static void main(String[] args) {\n" +
                "        System.out.println(\"main\");\n" +
                "    }\n" +
                "\n" +
                "    @AutoGenerated(uuid = \"generateOne\")\n" +
                "    public void generateOne() {\n" +
                "        System.out.println(\"new generateOne\");\n" +
                "    }\n" +
                "\n" +
                "    @AutoGenerated(uuid = \"generateOnePart\", locked = false)\n" +
                "    public void generateOnePart() throws TimeoutException, Throwable, Exception, RuntimeException, IllegalAccessException {\n" +
                "        /** This block is generated by vs, do not modify, start anchor 1 */\n" +
                "        System.out.println(\"test1\");\n" +
                "        System.out.println(\"test11\");\n" +
                "        test3.newGenerateTest();\n" +
                "        /** This block is generated by vs, do not modify, end anchor 1 */\n" +
                "        System.out.println(\"test3\");\n" +
                "\n" +
                "/** This block is generated by vs, do not modify, start anchor 2 */\n" +
                "        System.out.println(\"test2\");\n" +
                "        System.out.println(\"test2\");\n" +
                "        /** This block is generated by vs, do not modify, end anchor 2 */\n" +
                "\n" +
                "\n" +
                "/** This block is generated by vs, do not modify, start anchor 3 */\n" +
                "        System.out.println(\"test2\");\n" +
                "        /** This block is generated by vs, do not modify, end anchor 3 */\n" +
                "\n" +
                "    }\n" +
                "\n" +
                "    public void customMethodOne() {\n" +
                "        System.out.println(\"customMethodOne\");\n" +
                "    }\n" +
                "\n" +
                "    public void customMethodTwo() {\n" +
                "        System.out.println(\"customMethodTwo\");\n" +
                "    }\n" +
                "\n" +
                "    public void generateTwoPart() {\n" +
                "        /** This block is generated by vs, do not modify, start anchor 1 */\n" +
                "        System.out.println(\"test\");\n" +
                "        /** This block is generated by vs, do not modify, end anchor 1 */\n" +
                "    }\n" +
                "\n" +
                "    @AutoGenerated\n" +
                "    public void generateTwo() {\n" +
                "        System.out.println(\"generateTwo\");\n" +
                "    }\n" +
                "\n" +
                "    @AutoGenerated\n" +
                "    public void generateThree() {\n" +
                "        System.out.println(\"generateThree\");\n" +
                "    }\n" +
                "}", project)
//        val fileType = FileTypeManager.getInstance().getFileTypeByExtension("java")
//        val merger = AutoMerge(project, AutoMergeOptions(fileType, "package com.test_ssl.test_ssl.common.config;\n" +
//                "\n" +
//                "import com.meeting_manage.meeting.manager.bo.*;\n" +
//                "import com.meeting_manage.meeting.manager.dto.MeetingAgendaBaseDto;\n" +
//                "import com.meeting_manage.meeting.manager.dto.MeetingAgendaWithMeetingDto;\n" +
//                "import com.meeting_manage.meeting.manager.dto.MeetingBaseDto;\n" +
//                "import com.meeting_manage.meeting.service.base.BaseMeetingBOService;\n" +
//                "\n" +
//                "public class MeetingBOService extends BaseMeetingBOService {\n" +
//                "}", "package com.test_ssl.test_ssl.common.config;\n" +
//                "\n" +
//                "import com.meeting_manage.meeting.manager.bo.*;\n" +
//                "import com.meeting_manage.meeting.manager.dto.MeetingAgendaBaseDto;\n" +
//                "import com.meeting_manage.meeting.manager.dto.MeetingAgendaBaseDto1;\n" +
//                "import com.meeting_manage.meeting.manager.dto.MeetingAgendaWithMeetingDto;\n" +
//                "import com.meeting_manage.meeting.manager.dto.MeetingBaseDto;\n" +
//                "import com.meeting_manage.meeting.service.base.BaseMeetingBOService;\n" +
//                "\n" +
//                "public class MeetingBOService extends BaseMeetingBOService {\n" +
//                "}", "package com.test_ssl.test_ssl.common.config;\n" +
//                "\n" +
//                "import com.meeting_manage.meeting.manager.bo.*;\n" +
//                "import com.meeting_manage.meeting.manager.dto.MeetingAgendaBaseDto;\n" +
//                "import com.meeting_manage.meeting.manager.dto.MeetingAgendaBaseDto2;\n" +
//                "import com.meeting_manage.meeting.manager.dto.MeetingAgendaWithMeetingDto;\n" +
//                "import com.meeting_manage.meeting.manager.dto.MeetingBaseDto;\n" +
//                "import com.meeting_manage.meeting.service.base.BaseMeetingBOService;\n" +
//                "\n" +
//                "public class MeetingBOService extends BaseMeetingBOService {\n" +
//                "}"))
//
//        merger.merge {
//            println(it)
//        }

//        LOG.info("Opening plugin settings to application tab")
//        ShowSettingsUtil.getInstance().showSettingsDialog(
//            project,
//            CombinedPluginSettingsConfigurable::class.java
//        ) { configurable ->
//            configurable?.selectApplicationTab()
//        }
    }

    /**
     * Opens the project location in the default browser.
     * Constructs a URL with the project ID and encoded project name.
     */
    private fun openProjectLocationByBrowser() {
        try {
            // Get the project ID and encoded project name
            val projectId = projectSettings.projectId
            val encodedProjectName = URLEncoder.encode(projectSettings.projectName, "UTF-8")

            // Determine the appropriate protocol
            val protocol = getProtocol()

            // Retrieve the URL template from configuration and format it with project ID and name
            val pathAndParamsTemplate = ConfigUtil.getProperty("locator.desktop.url.setproject")
            val pathAndParams = MessageFormat.format(pathAndParamsTemplate, projectId, encodedProjectName)

            // Construct the full URL
            val url = "$protocol://$pathAndParams"

            // Log the URL and open it in the default browser
            LOG.info("Opening URL in browser: $url")
            BrowserUtil.browse(url)
        } catch (e: Exception) {
            // Log the exception if any error occurs
            LOG.error("Failed to open project location in browser", e)
        }
    }

    /**
     * Determines the appropriate protocol to use based on application settings.
     * @return The protocol string.
     */
    private fun getProtocol(): String {
        // Get the host from application settings
        val host = ApplicationPluginSettings.getInstance().host

        // Choose the protocol based on the host
        return if (host == ConfigUtil.getProperty("url.host.test")) {
            ConfigUtil.getProperty("locator.desktop.url.protocol.test")
        } else {
            ConfigUtil.getProperty("locator.desktop.url.protocol")
        }
    }

    /**
     * Shows the popup menu at the appropriate location relative to the status bar widget.
     * @param popup The JPopupMenu to be displayed.
     * @param event The MouseEvent that triggered the popup.
     */
    private fun showPopupMenu(popup: JPopupMenu, event: MouseEvent) {
        currentPopup = popup

        val statusBarComponent = statusBar?.component
        val ideFrame = WindowManager.getInstance().getIdeFrame(project)

        if (statusBarComponent != null && ideFrame != null) {
            val widgetLocation = event.component.locationOnScreen
            val popupX = widgetLocation.x
            val popupY = widgetLocation.y - popup.preferredSize.height

            val popupPoint = Point(
                popupX - statusBarComponent.locationOnScreen.x,
                popupY - statusBarComponent.locationOnScreen.y
            )

            popup.show(statusBarComponent, popupPoint.x, popupPoint.y)
        } else {
            // Fallback to default behavior if we can't get the status bar component or IDE frame
            popup.show(event.component, 0, -popup.preferredSize.height)
        }
    }

    private fun createFixCompileMenuItem(): JMenuItem {
        return JMenuItem("Fix Compile").apply {
            foreground = JBColor.YELLOW
            addActionListener {
                fixCompileErrors(getChangedFiles())
            }
        }
    }

    private fun getChangedFiles() : List<VirtualFile> {
        val changedFiles = ChangedFilesUtil.getChangedJavaFiles(project)
        println("Changed Java Files: $changedFiles")
        return changedFiles
    }

    private fun fixCompileErrors(changedFiles: List<VirtualFile>) {
        val quickFixCompiler = QuickFixCompiler(project)
        quickFixCompiler.applyQuickFixToVirtualFiles(changedFiles)
    }

}

package com.think1024.tocodesign.ideaplugin.startup

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.vfs.VirtualFileManager
import com.intellij.openapi.wm.ToolWindowManager
import com.intellij.ui.jcef.JBCefApp
import com.think1024.tocodesign.ideaplugin.constants.WindowIds
import com.think1024.tocodesign.ideaplugin.listeners.LocatorFileChangeListener
import com.think1024.tocodesign.ideaplugin.services.ModuleConfigService
import com.think1024.tocodesign.ideaplugin.services.NotificationPollingService
import com.think1024.tocodesign.ideaplugin.services.UserService
import com.think1024.tocodesign.ideaplugin.services.codebase.CodeBaseSearchManager
import com.think1024.tocodesign.ideaplugin.services.locator.LocatorIndexService
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.toco.TocoFileEditorManagerListener
import com.think1024.tocodesign.ideaplugin.toco.TocoWebViewLauncher
import com.think1024.tocodesign.ideaplugin.utils.GlobalExceptionHandler
import com.think1024.tocodesign.ideaplugin.utils.ModuleConfigUtil
import com.think1024.tocodesign.ideaplugin.utils.getPluginSettings
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.cef.CefApp

/**
 * This class implements StartupActivity and DumbAware to ensure it runs during the startup phase of the project.
 * If issues arise with ProjectManagerListener (e.g., scanAndCacheModules not running properly),
 * consider using this class instead by registering it in the plugin.xml.
 */
class ProjectOpenStartupActivity : TocoProjectActivity(), DumbAware {
    private val logger = Logger.getInstance(ProjectOpenStartupActivity::class.java)

    fun projectOpened(project: Project) {
        try {
            // Initialize the global exception handler within a try-catch block
            GlobalExceptionHandler.initialize()
        } catch (e: Exception) {
            logger.warn("Failed to initialize global exception handler. Continuing with project initialization.", e)
        }

        try {
            initializePluginSettings(project) // Initialize the plugin settings for the project

            // Retrieve the plugin settings for the project
            val projectSettings = project.getPluginSettings()

            // Check if the project ID is null or empty
            if (projectSettings.projectId.isNullOrEmpty()) {
                // Log an informational message and skip further initialization if the project ID is not set
                logger.warn("Project ID is null or empty, skipping further initialization.")
                return
            }

//            initModuleConfig(project) // Initialize module configurations
            initJBCefApp() // Initialize the JCEF (Java Chromium Embedded Framework) application
            startLoginStatusCheckService(project) // Start the service to check login status
            startNotificationPollingService(project) // Start the notification polling service
//            asyncScanLocatorIndex(project) // Perform an asynchronous scan of module configurations
//            asyncActivateCodebaseSearch(project)
//            registerLocatorFileChangeListener(project) // Register a listener for file change events related to the locator
        } catch (e: Exception) {
            // Log an error if any initialization step fails
            logger.error("Error during project opening initialization", e)
        }
    }

    /**
     * Initializes the ModuleConfigService for a given project.
     * This method will attempt to retrieve the ModuleConfigService instance,
     * triggering its initialization if it hasn't been already.
     *
     * @param project The project for which the ModuleConfigService should be initialized.
     */
    private fun initModuleConfig(project: Project) {
        try {
            // Retrieve the ModuleConfigService instance to trigger initialization
            project.service<ModuleConfigService>()
        } catch (e: Exception) {
            // Log a warning if the initialization fails
            logger.warn("Failed to initialize module configurations", e)
        }
    }

    /**
     * Initializes the plugin settings for the newly opened project.
     * This ensures that all necessary settings are properly set up and ready for use.
     *
     * @param project The Project instance for which to initialize settings.
     */
    private fun initializePluginSettings(project: Project) {
        try {
            project.getPluginSettings().ensureInitialized(project)
        } catch (e: Exception) {
            logger.warn("Failed to initialize plugin settings", e)
        }
    }

    /**
     * Initializes the JBCefApp and CefApp if supported.
     * This function checks for JCEF support and initializes the necessary components
     * on a background thread, ensuring that the main thread remains responsive.
     */
    private fun initJBCefApp() {
        // Launch a coroutine on the IO dispatcher for background processing
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // Check if JCEF is supported in the current IDE version
                if (JBCefApp.isSupported()) {
                    // Avoid static initialization issues by not calling getInstance() directly
                    withContext(Dispatchers.Main) {
                        // Initialize JBCefApp and CefApp on main thread to better control timing
                        JBCefApp.getInstance()
                        CefApp.getInstance()

                        // Log success message on the main thread
                        logger.info("JBCefApp and CefApp instances successfully initialized")
                    }
                } else {
                    // Log a warning if JCEF is not supported
                    withContext(Dispatchers.Main) {
                        logger.warn("JCEF is not supported in this IDE version")
                    }
                }
            } catch (e: Exception) {
                // Log an error if initialization fails
                withContext(Dispatchers.Main) {
                    logger.error("Failed to initialize JCEF application", e)
                }
            }
        }
    }

    /**
     * Starts the login status check service for a specified project.
     * This function initiates the login check task on a background thread,
     * ensuring that the main thread is not blocked during the operation.
     *
     * @param project The IntelliJ IDEA project for which the login status check service is to be started.
     */
    private fun startLoginStatusCheckService(project: Project) {
        // Use invokeLater to ensure that the login check task is started after all configurations are ready
        ApplicationManager.getApplication().invokeLater {
            // Launch a coroutine on the IO dispatcher for background processing
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    // Retrieve the user service instance
                    val userService = UserService.getInstance()
                    // Start the login check task for the project
                    userService.startLoginCheckTask(project)
                } catch (e: Exception) {
                    // If there is an exception, log a warning on the main thread
                    withContext(Dispatchers.Main) {
                        logger.warn("Error in login status check service", e)
                    }
                }
            }
        }
    }

    /**
     * 启动通知轮询服务
     * 在项目打开时自动启动轮询，而不是等到工具窗口创建
     */
    private fun startNotificationPollingService(project: Project) {
        try {
            val pollingService = NotificationPollingService.getInstance(project)
            pollingService.startPolling()
            logger.info("Notification polling service started for project: ${project.name}")
        } catch (e: Exception) {
            logger.warn("Failed to start notification polling service", e)
        }
    }

    /**
     * Asynchronously scans the locator index for the project.
     * This method is responsible for scanning the locator index for the project in a background thread.
     *
     * @param project The Project instance for which to scan the locator index.
     */
    private fun asyncScanLocatorIndex(project: Project) {
        try {
            LocatorIndexService(project)
        } catch (e: Exception) {
            logger.warn("Failed to scan locator index", e)
        }
    }

    private fun asyncActivateCodebaseSearch(project: Project) {
        try {
            project.service<CodeBaseSearchManager>()
        } catch (e: Exception) {
            logger.warn("Failed to activate CodebaseSearch", e)
        }
    }

    /**
     * Registers the LocatorFileChangeListener for the project.
     * This listener is responsible for handling file change events within the project.
     *
     * @param project The Project instance for which to register the file change listener.
     */
    private fun registerLocatorFileChangeListener(project: Project) {
        try {
            val connection = project.messageBus.connect()
            connection.subscribe(
                VirtualFileManager.VFS_CHANGES, LocatorFileChangeListener(project)
            )
        } catch (e: Exception) {
            logger.warn("Failed to register LocatorFileChangeListener", e)
        }
    }

    override fun runActivity(project: Project) {
        if (ProjectPluginSettings.getInstance(project).projectId == null) {
            return
        }
        projectOpened(project)
        var listener = TocoFileEditorManagerListener.getInstance(project)

        ApplicationManager.getApplication().invokeLater {
            TocoWebViewLauncher.restoreOpenedFiles(project)
            listener.checkCurrentTab()

            val manager = ToolWindowManager.getInstance(project)
            val toolWindow = manager.getToolWindow(WindowIds.TOCO_MENU)

            // ✅ 关键操作：确保 toolWindow 初始化
            if (toolWindow != null && !toolWindow.isVisible) {
                // 这个调用会触发 ToolWindowFactory.createToolWindowContent
                toolWindow.show(null)  // 这里会显示 UI，但我们可以在后面立刻 hide 掉
                toolWindow.hide(null)  // 实现“后台初始化而不弹出 UI”
            }
        }

    }
}

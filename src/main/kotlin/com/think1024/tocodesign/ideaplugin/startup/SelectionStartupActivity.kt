package com.think1024.tocodesign.ideaplugin.startup

import com.intellij.openapi.project.Project
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.editor.event.SelectionEvent
import com.intellij.openapi.editor.event.SelectionListener
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.FileEditorManagerEvent
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiDocumentManager
import com.think1024.tocodesign.ideaplugin.settings.ProjectPluginSettings
import com.think1024.tocodesign.ideaplugin.webview.WebViewBridge
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class SelectionStartupActivity : TocoProjectActivity() {
    // 跟踪当前激活的编辑器
    private var activeEditor: Editor? = null

    @OptIn(DelicateCoroutinesApi::class)
    override fun runActivity(project: Project) {
        if (ProjectPluginSettings.getInstance(project).projectId == null) {
            return
        }

        val multicaster = EditorFactory.getInstance().eventMulticaster
        setupEditorActivationListener(project)

        multicaster.addSelectionListener(object : SelectionListener {
            override fun selectionChanged(e: SelectionEvent) {
                val editor = e.editor
                val selectedText = editor.selectionModel.selectedText

                // 只有当前激活的编辑器才处理选区变化
                if (editor != activeEditor) {
                    return
                }

                // 检查编辑器所属文件类型和上下文
                val project = editor.project ?: return
                val editorFile = FileDocumentManager.getInstance().getFile(editor.document)

                // 排除非项目文件或特殊编辑器
                if (editorFile == null || !editorFile.isValid || !editorFile.isInLocalFileSystem) {
                    return  // 排除虚拟文件或非本地文件系统的文件
                }

                val psiFile = PsiDocumentManager.getInstance(project).getPsiFile(editor.document)
                if (psiFile !== null) {
//                    println("✅ 当前选中区域: $selectedText")

                    // 获取文件信息
                    val fileInfo = createFileInfo(editorFile, selectedText ?: "", project)

                    if (selectedText.isNullOrBlank()) {
                        println("✅ 当前选中区域已清除")
                    } else {
                        println("✅ 当前选中区域: $selectedText")
                    }

                    // 启动协程，异步向 WebView 发送消息
                    GlobalScope.launch {
                        try {
                            WebViewBridge.sendIfReady(project, "TocoDesign", "selection-changed", fileInfo)
                        } catch (e: Exception) {
                            println("❌ WebView 请求失败: ${e.message}")
                        }
                    }
                }
            }
        }, project)

        println("✅ SelectionListener 已注册")
    }

    /**
     * 设置编辑器激活状态监听器
     */
    private fun setupEditorActivationListener(project: Project) {
        // 监听编辑器激活状态变化
        project.messageBus.connect().subscribe(
            FileEditorManagerListener.FILE_EDITOR_MANAGER,
            object : FileEditorManagerListener {
                override fun selectionChanged(event: FileEditorManagerEvent) {
                    val file = event.newFile
                    if (file != null) {
                        // 获取当前激活的编辑器
                        val editor = FileEditorManager.getInstance(project).getSelectedTextEditor()
                        activeEditor = editor

                        // 无论是否有选区，都发送当前状态（包括空选区）
                        editor?.let { sendSelectionState(it, project) } ?: run {
                            // 如果没有编辑器，发送空状态
                            sendEmptySelection(project, file)
                        }
                    } else {
                        activeEditor = null
                        // 没有选中的文件，发送空状态
                        sendEmptySelection(project, null)
                    }
                }
            }
        )
    }

    /**
     * 发送当前编辑器的选区状态（无论是否有选区）
     */
    private fun sendSelectionState(editor: Editor, project: Project) {
        val selectedText = editor.selectionModel.selectedText
        val editorFile = FileDocumentManager.getInstance().getFile(editor.document) ?: return

        if (!editorFile.isValid || !editorFile.isInLocalFileSystem) {
            return
        }

        val fileInfo = createFileInfo(editorFile, selectedText ?: "", project)

        @OptIn(DelicateCoroutinesApi::class)
        GlobalScope.launch {
            try {
                WebViewBridge.sendIfReady(project, "TocoDesign", "selection-changed", fileInfo)
                if (selectedText.isNullOrBlank()) {
                    println("✅ 切换到激活标签页，无选区，发送清除信息")
                } else {
                    println("✅ 切换到激活标签页，发送选区内容: $selectedText")
                }
            } catch (e: Exception) {
                println("❌ 切换标签页发送选区状态失败: ${e.message}")
            }
        }
    }

    /**
     * 发送空选区信息
     */
    private fun sendEmptySelection(project: Project, file: VirtualFile?) {
        val fileInfo = if (file != null && file.isValid && file.isInLocalFileSystem) {
            createFileInfo(file, "", project)
        } else {
            mapOf(
                "name" to "",
                "path" to "",
                "content" to ""
            )
        }

        @OptIn(DelicateCoroutinesApi::class)
        GlobalScope.launch {
            try {
                WebViewBridge.sendIfReady(project, "TocoDesign", "selection-changed", fileInfo)
                println("✅ 发送空选区信息以清除原有内容")
            } catch (e: Exception) {
                println("❌ 发送空选区信息失败: ${e.message}")
            }
        }
    }

    /**
     * 创建包含文件信息的Map
     */
    private fun createFileInfo(file: VirtualFile, content: String, project: Project): Map<String, String> {
        val projectRelativePath = getProjectRelativePath(project, file)
        return mapOf(
            "name" to file.name,
            "path" to projectRelativePath,
            "content" to content
        )
    }

    private fun getProjectRelativePath(project: Project, file: VirtualFile): String {
        val fullPath = file.path
        val projectName = project.name
        val projectNameWithSeparator = "/$projectName/"

        val projectNameIndex = fullPath.indexOf(projectNameWithSeparator)

        if (projectNameIndex != -1) {
            val relativePath = fullPath.substring(projectNameIndex + projectNameWithSeparator.length)
            return relativePath
        } else {
            println("无法在路径中找到项目名称: $projectName，路径: $fullPath")
            return fullPath
        }
    }
}
